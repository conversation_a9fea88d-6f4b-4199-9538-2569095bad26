<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 基本信息 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="基本信息"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="8dp"
            android:hint="配置名称 *">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_config_name"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="text" />

        </com.google.android.material.textfield.TextInputLayout>

        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="配置描述">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_config_description"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:inputType="textMultiLine"
                android:lines="2"
                android:maxLines="3" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 保质期范围 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保质期范围"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="最小天数 *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_min_shelf_life_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="最大天数 *">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_max_shelf_life_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 提醒设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="提醒设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:hint="临期提醒（天）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_warning_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:hint="下架提醒（天）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_removal_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:hint="折扣提醒（天）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_discount_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 通知设置 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="通知设置"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginTop="8dp"
            android:layout_marginBottom="12dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="通知频率（小时）">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_notification_frequency"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:hint="通知时间">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_notification_time"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:focusable="false"
                    android:clickable="true"
                    android:inputType="none" />

            </com.google.android.material.textfield.TextInputLayout>

        </LinearLayout>

        <!-- 开关设置 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginTop="16dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="8dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="启用此配置"
                    android:textSize="16sp" />

                <Switch
                    android:id="@+id/switch_enabled"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="启用通知提醒"
                    android:textSize="16sp" />

                <Switch
                    android:id="@+id/switch_notification_enabled"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content" />

            </LinearLayout>

        </LinearLayout>

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
