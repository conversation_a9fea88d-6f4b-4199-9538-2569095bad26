<?xml version="1.0" encoding="utf-8"?>
<androidx.coordinatorlayout.widget.CoordinatorLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 主内容区域 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="match_parent"
        android:layout_height="match_parent">

        <androidx.core.widget.NestedScrollView
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:fillViewport="true">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <!-- 通用设置 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/card_background">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="通用设置"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="16dp" />

                        <!-- 默认保质期提醒天数 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="默认临期提醒"
                                    android:textSize="16sp"
                                    android:textColor="@color/text_primary" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="商品到期前几天开始提醒"
                                    android:textSize="12sp"
                                    android:textColor="@color/text_secondary" />

                            </LinearLayout>

                            <com.google.android.material.textfield.TextInputLayout
                                android:layout_width="80dp"
                                android:layout_height="wrap_content"
                                android:hint="天数">

                                <com.google.android.material.textfield.TextInputEditText
                                    android:id="@+id/edit_text_default_warning_days"
                                    android:layout_width="match_parent"
                                    android:layout_height="wrap_content"
                                    android:inputType="number"
                                    android:text="7" />

                            </com.google.android.material.textfield.TextInputLayout>

                        </LinearLayout>

                        <!-- 通知频率 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="通知频率"
                                    android:textSize="16sp"
                                    android:textColor="@color/text_primary" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="每隔多长时间检查一次"
                                    android:textSize="12sp"
                                    android:textColor="@color/text_secondary" />

                            </LinearLayout>

                            <Spinner
                                android:id="@+id/spinner_notification_frequency"
                                android:layout_width="120dp"
                                android:layout_height="wrap_content" />

                        </LinearLayout>

                        <!-- 启用通知 -->
                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical">

                            <LinearLayout
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:orientation="vertical">

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="启用通知"
                                    android:textSize="16sp"
                                    android:textColor="@color/text_primary" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="接收保质期提醒通知"
                                    android:textSize="12sp"
                                    android:textColor="@color/text_secondary" />

                            </LinearLayout>

                            <com.google.android.material.switchmaterial.SwitchMaterial
                                android:id="@+id/switch_enable_notifications"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:checked="true" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 保质期配置 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/card_background">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <LinearLayout
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:orientation="horizontal"
                            android:gravity="center_vertical"
                            android:layout_marginBottom="16dp">

                            <TextView
                                android:layout_width="0dp"
                                android:layout_height="wrap_content"
                                android:layout_weight="1"
                                android:text="保质期配置"
                                android:textSize="18sp"
                                android:textStyle="bold"
                                android:textColor="@color/text_primary" />

                            <Button
                                android:id="@+id/button_add_config"
                                style="@style/Widget.Material3.Button.TextButton"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="添加配置"
                                app:icon="@drawable/ic_add_24" />

                        </LinearLayout>

                        <androidx.recyclerview.widget.RecyclerView
                            android:id="@+id/recycler_view_configs"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:nestedScrollingEnabled="false"
                            tools:listitem="@layout/item_config" />

                        <!-- 空状态 -->
                        <LinearLayout
                            android:id="@+id/layout_empty_state"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:gravity="center"
                            android:orientation="vertical"
                            android:padding="32dp"
                            android:visibility="gone">

                            <TextView
                                android:id="@+id/text_view_empty"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="暂无保质期配置"
                                android:textColor="@color/text_hint"
                                android:textSize="14sp"
                                android:layout_marginBottom="16dp" />

                            <Button
                                android:id="@+id/button_add_first_config"
                                style="@style/Widget.Material3.Button.OutlinedButton"
                                android:layout_width="wrap_content"
                                android:layout_height="wrap_content"
                                android:text="添加第一个配置" />

                        </LinearLayout>

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

                <!-- 数据管理 -->
                <com.google.android.material.card.MaterialCardView
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    app:cardCornerRadius="16dp"
                    app:cardElevation="2dp"
                    app:cardBackgroundColor="@color/card_background">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="vertical"
                        android:padding="16dp">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="数据管理"
                            android:textSize="18sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary"
                            android:layout_marginBottom="16dp" />

                        <Button
                            android:id="@+id/button_export_data"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="导出数据"
                            app:icon="@android:drawable/ic_menu_save" />

                        <Button
                            android:id="@+id/button_import_data"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:layout_marginBottom="8dp"
                            android:text="导入数据"
                            app:icon="@android:drawable/ic_menu_upload" />

                        <Button
                            android:id="@+id/button_clear_data"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:text="清空所有数据"
                            android:textColor="@color/danger"
                            app:icon="@android:drawable/ic_menu_delete"
                            app:iconTint="@color/danger"
                            app:strokeColor="@color/danger" />

                    </LinearLayout>

                </com.google.android.material.card.MaterialCardView>

            </LinearLayout>

        </androidx.core.widget.NestedScrollView>

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="center"
        android:visibility="gone" />

    <!-- 添加配置按钮 -->
    <com.google.android.material.floatingactionbutton.FloatingActionButton
        android:id="@+id/fab_add_config"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:layout_gravity="bottom|end"
        android:layout_margin="16dp"
        android:contentDescription="添加配置"
        app:srcCompat="@android:drawable/ic_input_add" />

</androidx.coordinatorlayout.widget.CoordinatorLayout>
