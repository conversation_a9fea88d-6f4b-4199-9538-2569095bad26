package com.example.dataassistant.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.dataassistant.data.entity.ShelfLifeConfig

/**
 * 保质期配置数据访问对象
 */
@Dao
interface ShelfLifeConfigDao {

    /**
     * 获取所有配置
     */
    @Query("SELECT * FROM shelf_life_configs ORDER BY isDefault DESC, createTime DESC")
    fun getAllConfigs(): LiveData<List<ShelfLifeConfig>>

    /**
     * 获取所有配置（同步版本）
     */
    @Query("SELECT * FROM shelf_life_configs ORDER BY isDefault DESC, createTime DESC")
    suspend fun getAllConfigsSync(): List<ShelfLifeConfig>

    /**
     * 获取启用的配置
     */
    @Query("SELECT * FROM shelf_life_configs WHERE isEnabled = 1 ORDER BY isDefault DESC, createTime DESC")
    fun getEnabledConfigs(): LiveData<List<ShelfLifeConfig>>

    /**
     * 获取默认配置
     */
    @Query("SELECT * FROM shelf_life_configs WHERE isDefault = 1 LIMIT 1")
    suspend fun getDefaultConfig(): ShelfLifeConfig?

    /**
     * 根据ID获取配置
     */
    @Query("SELECT * FROM shelf_life_configs WHERE id = :id")
    suspend fun getConfigById(id: String): ShelfLifeConfig?

    /**
     * 根据名称搜索配置
     */
    @Query("SELECT * FROM shelf_life_configs WHERE name LIKE '%' || :keyword || '%' ORDER BY createTime DESC")
    suspend fun searchConfigs(keyword: String): List<ShelfLifeConfig>

    /**
     * 插入配置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConfig(config: ShelfLifeConfig)

    /**
     * 批量插入配置
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertConfigs(configs: List<ShelfLifeConfig>)

    /**
     * 更新配置
     */
    @Update
    suspend fun updateConfig(config: ShelfLifeConfig)

    /**
     * 删除配置
     */
    @Delete
    suspend fun deleteConfig(config: ShelfLifeConfig)

    /**
     * 根据ID删除配置
     */
    @Query("DELETE FROM shelf_life_configs WHERE id = :id")
    suspend fun deleteConfigById(id: String)

    /**
     * 设置默认配置（先清除所有默认标记，再设置新的默认配置）
     */
    @Transaction
    suspend fun setDefaultConfig(configId: String) {
        clearDefaultConfig()
        setConfigAsDefault(configId)
    }

    /**
     * 清除所有默认配置标记
     */
    @Query("UPDATE shelf_life_configs SET isDefault = 0")
    suspend fun clearDefaultConfig()

    /**
     * 设置指定配置为默认
     */
    @Query("UPDATE shelf_life_configs SET isDefault = 1 WHERE id = :configId")
    suspend fun setConfigAsDefault(configId: String)



    /**
     * 启用/禁用配置
     */
    @Query("UPDATE shelf_life_configs SET isEnabled = :enabled, updateTime = :updateTime WHERE id = :id")
    suspend fun updateConfigEnabled(id: String, enabled: Boolean, updateTime: Long)

    /**
     * 获取配置总数
     */
    @Query("SELECT COUNT(*) FROM shelf_life_configs")
    suspend fun getConfigCount(): Int

    /**
     * 清空所有配置
     */
    @Query("DELETE FROM shelf_life_configs")
    suspend fun deleteAllConfigs()
}
