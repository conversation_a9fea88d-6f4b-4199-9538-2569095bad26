package com.example.dataassistant.data.repository

import androidx.lifecycle.LiveData
import com.example.dataassistant.data.dao.ProductDao
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 商品数据仓库
 */
@Singleton
class ProductRepository @Inject constructor(
    private val productDao: ProductDao
) {

    /**
     * 获取所有商品
     */
    fun getAllProducts(): LiveData<List<Product>> = productDao.getAllProducts()

    /**
     * 根据状态获取商品
     */
    fun getProductsByStatus(status: ProductStatus): LiveData<List<Product>> =
        productDao.getProductsByStatus(status)

    /**
     * 获取即将过期的商品
     */
    fun getExpiringProducts(days: Int): LiveData<List<Product>> =
        productDao.getExpiringProducts(System.currentTimeMillis(), days)

    /**
     * 获取已过期的商品
     */
    fun getExpiredProducts(): LiveData<List<Product>> =
        productDao.getExpiredProducts(System.currentTimeMillis())

    /**
     * 根据分类获取商品
     */
    fun getProductsByCategory(category: String): LiveData<List<Product>> =
        productDao.getProductsByCategory(category)

    /**
     * 搜索商品
     */
    fun searchProducts(keyword: String): LiveData<List<Product>> =
        productDao.searchProducts(keyword)

    /**
     * 根据ID获取商品
     */
    suspend fun getProductById(id: String): Product? = productDao.getProductById(id)

    /**
     * 根据条码获取商品
     */
    suspend fun getProductByBarcode(barcode: String): List<Product> =
        productDao.getProductByBarcode(barcode)

    /**
     * 插入商品
     */
    suspend fun insertProduct(product: Product) = productDao.insertProduct(product)

    /**
     * 批量插入商品
     */
    suspend fun insertProducts(products: List<Product>) = productDao.insertProducts(products)

    /**
     * 更新商品
     */
    suspend fun updateProduct(product: Product) = productDao.updateProduct(product)

    /**
     * 删除商品
     */
    suspend fun deleteProduct(product: Product) = productDao.deleteProduct(product)

    /**
     * 根据ID删除商品
     */
    suspend fun deleteProductById(id: String) = productDao.deleteProductById(id)

    /**
     * 批量删除商品
     */
    suspend fun deleteProductsByIds(ids: List<String>) = productDao.deleteProductsByIds(ids)

    /**
     * 更新商品状态
     */
    suspend fun updateProductStatus(id: String, status: ProductStatus) =
        productDao.updateProductStatus(id, status, System.currentTimeMillis())

    /**
     * 获取商品统计信息
     */
    suspend fun getTotalCount(): Int = productDao.getTotalCount()

    suspend fun getCountByStatus(status: ProductStatus): Int = productDao.getCountByStatus(status)

    suspend fun getCountByCategory(category: String): Int = productDao.getCountByCategory(category)

    /**
     * 获取所有分类
     */
    suspend fun getAllCategories(): List<String> = productDao.getAllCategories()

    /**
     * 获取所有品牌
     */
    suspend fun getAllBrands(): List<String> = productDao.getAllBrands()

    /**
     * 获取即将过期商品数量
     */
    suspend fun getExpiringProductsCount(days: Int): Int =
        productDao.getExpiringProductsCount(System.currentTimeMillis(), days)

    /**
     * 获取最近添加的商品
     */
    suspend fun getRecentProducts(limit: Int = 10): List<Product> =
        productDao.getRecentProducts(limit)

    /**
     * 清空所有商品
     */
    suspend fun deleteAllProducts() = productDao.deleteAllProducts()
}
