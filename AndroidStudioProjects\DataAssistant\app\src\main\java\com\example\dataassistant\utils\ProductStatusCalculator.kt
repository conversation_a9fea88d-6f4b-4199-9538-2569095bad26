package com.example.dataassistant.utils

import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.entity.ShelfLifeConfig
import java.util.concurrent.TimeUnit

/**
 * 商品状态计算工具类
 */
object ProductStatusCalculator {
    
    /**
     * 计算商品状态
     */
    fun calculateProductStatus(product: Product, config: ShelfLifeConfig? = null): ProductStatusInfo {
        val currentTime = System.currentTimeMillis()
        val expiryTime = product.expiryDate
        val remainingTime = expiryTime - currentTime
        val remainingDays = TimeUnit.MILLISECONDS.toDays(remainingTime).toInt()
        
        // 使用配置或商品自身的提醒设置
        val warningDays = config?.warningDays ?: product.warningDays
        val removalDays = config?.removalDays ?: product.removalDays
        
        val status = when {
            remainingDays < 0 -> ProductStatus.EXPIRED
            remainingDays <= removalDays -> ProductStatus.REMOVAL
            remainingDays <= warningDays -> ProductStatus.WARNING
            else -> ProductStatus.NORMAL
        }
        
        return ProductStatusInfo(
            status = status,
            remainingDays = remainingDays,
            statusText = getStatusText(status, remainingDays),
            statusColor = getStatusColor(status),
            isExpired = remainingDays < 0,
            isWarning = remainingDays in 0..warningDays,
            needsRemoval = remainingDays in 0..removalDays
        )
    }
    
    /**
     * 批量计算商品状态
     */
    fun calculateProductStatuses(
        products: List<Product>, 
        configs: Map<String, ShelfLifeConfig> = emptyMap()
    ): List<ProductWithStatus> {
        return products.map { product ->
            val config = product.configId?.let { configs[it] }
            val statusInfo = calculateProductStatus(product, config)
            ProductWithStatus(product, statusInfo)
        }
    }
    
    /**
     * 获取状态文本
     */
    private fun getStatusText(status: ProductStatus, remainingDays: Int): String {
        return when (status) {
            ProductStatus.NORMAL -> "正常"
            ProductStatus.WARNING -> "临期 (${remainingDays}天)"
            ProductStatus.EXPIRED -> "已过期"
            ProductStatus.REMOVAL -> "需下架 (${remainingDays}天)"
        }
    }
    
    /**
     * 获取状态颜色
     */
    private fun getStatusColor(status: ProductStatus): String {
        return when (status) {
            ProductStatus.NORMAL -> "#4CAF50"      // 绿色
            ProductStatus.WARNING -> "#FF9800"     // 橙色
            ProductStatus.EXPIRED -> "#F44336"     // 红色
            ProductStatus.REMOVAL -> "#E91E63"     // 粉红色
        }
    }
    
    /**
     * 计算保质期天数
     */
    fun calculateShelfLifeDays(shelfLifeNumber: Int, shelfLifeUnit: String): Int {
        return when (shelfLifeUnit.lowercase()) {
            "day", "天" -> shelfLifeNumber
            "week", "周" -> shelfLifeNumber * 7
            "month", "月" -> shelfLifeNumber * 30
            "year", "年" -> shelfLifeNumber * 365
            else -> shelfLifeNumber
        }
    }
    
    /**
     * 计算到期日期
     */
    fun calculateExpiryDate(productionDate: Long, shelfLifeDays: Int): Long {
        return productionDate + TimeUnit.DAYS.toMillis(shelfLifeDays.toLong())
    }
    
    /**
     * 检查商品是否需要提醒
     */
    fun shouldNotify(product: Product, config: ShelfLifeConfig? = null): Boolean {
        if (!product.isNotificationEnabled) return false
        
        val statusInfo = calculateProductStatus(product, config)
        return statusInfo.isWarning || statusInfo.isExpired || statusInfo.needsRemoval
    }
    
    /**
     * 获取提醒消息
     */
    fun getNotificationMessage(product: Product, config: ShelfLifeConfig? = null): String? {
        val statusInfo = calculateProductStatus(product, config)
        
        return when {
            statusInfo.isExpired -> "商品「${product.name}」已过期，请及时处理"
            statusInfo.needsRemoval -> "商品「${product.name}」需要下架，剩余${statusInfo.remainingDays}天"
            statusInfo.isWarning -> "商品「${product.name}」即将过期，剩余${statusInfo.remainingDays}天"
            else -> null
        }
    }
}

/**
 * 商品状态信息
 */
data class ProductStatusInfo(
    val status: ProductStatus,
    val remainingDays: Int,
    val statusText: String,
    val statusColor: String,
    val isExpired: Boolean,
    val isWarning: Boolean,
    val needsRemoval: Boolean
)

/**
 * 带状态的商品
 */
data class ProductWithStatus(
    val product: Product,
    val statusInfo: ProductStatusInfo
)
