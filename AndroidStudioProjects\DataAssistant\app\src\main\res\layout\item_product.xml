<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="4dp"
    app:cardCornerRadius="8dp"
    app:cardElevation="2dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="horizontal"
        android:padding="12dp">

        <!-- 状态指示器 -->
        <View
            android:id="@+id/view_status_indicator"
            android:layout_width="4dp"
            android:layout_height="match_parent"
            android:layout_marginEnd="12dp"
            android:background="@android:color/holo_green_light" />

        <!-- 商品信息 -->
        <LinearLayout
            android:layout_width="0dp"
            android:layout_height="wrap_content"
            android:layout_weight="1"
            android:orientation="vertical">

            <!-- 商品名称和品牌 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical">

                <TextView
                    android:id="@+id/text_view_product_name"
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="商品名称"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:maxLines="1"
                    android:ellipsize="end"
                    tools:text="新希望低温牛奶" />

                <TextView
                    android:id="@+id/text_view_brand"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="品牌"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    android:background="@drawable/bg_brand_tag"
                    android:padding="4dp"
                    android:layout_marginStart="8dp"
                    android:visibility="gone"
                    tools:text="新希望"
                    tools:visibility="visible" />

            </LinearLayout>

            <!-- 分类和数量 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="4dp">

                <TextView
                    android:id="@+id/text_view_category"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="分类"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    tools:text="低温奶" />

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text=" • "
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

                <TextView
                    android:id="@+id/text_view_quantity"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="数量: 1"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray"
                    tools:text="数量: 2" />

            </LinearLayout>

            <!-- 日期信息 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:layout_marginTop="8dp">

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="生产日期"
                        android:textSize="10sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/text_view_production_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2024-01-01"
                        android:textSize="12sp"
                        tools:text="2024-01-01" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:orientation="vertical">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="到期日期"
                        android:textSize="10sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/text_view_expiry_date"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="2024-01-15"
                        android:textSize="12sp"
                        tools:text="2024-01-15" />

                </LinearLayout>

            </LinearLayout>

        </LinearLayout>

        <!-- 状态和剩余天数 -->
        <LinearLayout
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:gravity="center"
            android:layout_marginStart="12dp">

            <TextView
                android:id="@+id/text_view_status"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正常"
                android:textSize="12sp"
                android:textStyle="bold"
                android:textColor="@android:color/holo_green_dark"
                tools:text="临期" />

            <TextView
                android:id="@+id/text_view_remaining_days"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="还有7天"
                android:textSize="10sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginTop="2dp"
                tools:text="还有3天" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
