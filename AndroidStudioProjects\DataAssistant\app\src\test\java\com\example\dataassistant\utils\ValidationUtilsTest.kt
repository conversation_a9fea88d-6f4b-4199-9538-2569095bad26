package com.example.dataassistant.utils

import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.entity.ShelfLifeConfig
import org.junit.Assert.*
import org.junit.Test
import java.util.*

/**
 * ValidationUtils单元测试
 */
class ValidationUtilsTest {

    @Test
    fun testValidateProductName() {
        // 测试有效的商品名称
        val validResult = ValidationUtils.validateProductName("苹果")
        assertTrue(validResult.isValid)
        assertTrue(validResult.errors.isEmpty())

        // 测试空名称
        val emptyResult = ValidationUtils.validateProductName("")
        assertFalse(emptyResult.isValid)
        assertTrue(emptyResult.errors.contains("商品名称不能为空"))

        // 测试过长名称
        val longName = "a".repeat(101)
        val longResult = ValidationUtils.validateProductName(longName)
        assertFalse(longResult.isValid)
        assertTrue(longResult.errors.contains("商品名称不能超过100个字符"))
    }

    @Test
    fun testValidateQuantity() {
        // 测试有效数量
        val validResult = ValidationUtils.validateQuantity(5)
        assertTrue(validResult.isValid)

        // 测试零数量
        val zeroResult = ValidationUtils.validateQuantity(0)
        assertFalse(zeroResult.isValid)
        assertTrue(zeroResult.errors.contains("商品数量必须大于0"))

        // 测试负数量
        val negativeResult = ValidationUtils.validateQuantity(-1)
        assertFalse(negativeResult.isValid)
        assertTrue(negativeResult.errors.contains("商品数量必须大于0"))
    }

    @Test
    fun testValidateShelfLifeDays() {
        // 测试有效保质期
        val validResult = ValidationUtils.validateShelfLifeDays(30)
        assertTrue(validResult.isValid)

        // 测试零保质期
        val zeroResult = ValidationUtils.validateShelfLifeDays(0)
        assertFalse(zeroResult.isValid)
        assertTrue(zeroResult.errors.contains("保质期天数必须大于0"))

        // 测试过长保质期
        val longResult = ValidationUtils.validateShelfLifeDays(10001)
        assertFalse(longResult.isValid)
        assertTrue(longResult.errors.contains("保质期天数不能超过10000天"))
    }

    @Test
    fun testValidateDates() {
        val now = System.currentTimeMillis()
        val yesterday = now - 24 * 60 * 60 * 1000
        val tomorrow = now + 24 * 60 * 60 * 1000

        // 测试有效日期
        val validResult = ValidationUtils.validateDates(yesterday, tomorrow, now)
        assertTrue(validResult.isValid)

        // 测试生产日期晚于到期日期
        val invalidResult = ValidationUtils.validateDates(tomorrow, yesterday, now)
        assertFalse(invalidResult.isValid)
        assertTrue(invalidResult.errors.contains("生产日期不能晚于到期日期"))

        // 测试生产日期在未来
        val futureResult = ValidationUtils.validateDates(tomorrow, tomorrow + 24 * 60 * 60 * 1000, now)
        assertFalse(futureResult.isValid)
        assertTrue(futureResult.errors.contains("生产日期不能是未来时间"))
    }

    @Test
    fun testValidateProduct() {
        val validProduct = Product(
            id = UUID.randomUUID().toString(),
            name = "测试商品",
            productionDate = System.currentTimeMillis() - 24 * 60 * 60 * 1000,
            expiryDate = System.currentTimeMillis() + 30 * 24 * 60 * 60 * 1000,
            stockDate = System.currentTimeMillis(),
            quantity = 1,
            shelfLifeDays = 30,
            shelfLifeNumber = 30,
            shelfLifeUnit = "天",
            warningDays = 3,
            removalDays = 1,
            status = ProductStatus.NORMAL,
            isNotificationEnabled = true,
            createTime = System.currentTimeMillis(),
            updateTime = System.currentTimeMillis()
        )

        val validResult = ValidationUtils.validateProduct(validProduct)
        assertTrue(validResult.isValid)

        // 测试无效商品
        val invalidProduct = validProduct.copy(name = "", quantity = 0)
        val invalidResult = ValidationUtils.validateProduct(invalidProduct)
        assertFalse(invalidResult.isValid)
        assertTrue(invalidResult.errors.size > 1)
    }

    @Test
    fun testValidateShelfLifeConfig() {
        val validConfig = ShelfLifeConfig(
            id = UUID.randomUUID().toString(),
            name = "测试配置",
            description = "测试描述",
            minShelfLifeDays = 1,
            maxShelfLifeDays = 365,
            warningDays = 3,
            removalDays = 1,
            discountDays = 7,
            notificationFrequency = 24,
            notificationTime = "09:00",
            isEnabled = true,
            isNotificationEnabled = true,
            isDefault = false,
            createTime = System.currentTimeMillis(),
            updateTime = System.currentTimeMillis()
        )

        val validResult = ValidationUtils.validateShelfLifeConfig(validConfig)
        assertTrue(validResult.isValid)

        // 测试无效配置
        val invalidConfig = validConfig.copy(
            name = "",
            minShelfLifeDays = 0,
            maxShelfLifeDays = -1,
            notificationTime = "25:00"
        )
        val invalidResult = ValidationUtils.validateShelfLifeConfig(invalidConfig)
        assertFalse(invalidResult.isValid)
        assertTrue(invalidResult.errors.size > 1)
    }

    @Test
    fun testValidateBarcode() {
        // 测试有效条码
        val validBarcodes = listOf("12345678", "123456789012", "1234567890123", "12345678901234")
        validBarcodes.forEach { barcode ->
            val result = ValidationUtils.validateBarcode(barcode)
            assertTrue("Barcode $barcode should be valid", result.isValid)
        }

        // 测试无效条码
        val invalidBarcodes = listOf("", "1234567", "123456789012345", "12345abc", "abcdefgh")
        invalidBarcodes.forEach { barcode ->
            val result = ValidationUtils.validateBarcode(barcode)
            assertFalse("Barcode $barcode should be invalid", result.isValid)
        }
    }

    @Test
    fun testValidateEmail() {
        // 测试有效邮箱
        val validEmails = listOf("<EMAIL>", "<EMAIL>", "<EMAIL>")
        validEmails.forEach { email ->
            val result = ValidationUtils.validateEmail(email)
            assertTrue("Email $email should be valid", result.isValid)
        }

        // 测试无效邮箱
        val invalidEmails = listOf("", "invalid", "test@", "@domain.com", "test.domain.com")
        invalidEmails.forEach { email ->
            val result = ValidationUtils.validateEmail(email)
            assertFalse("Email $email should be invalid", result.isValid)
        }
    }

    @Test
    fun testValidatePhoneNumber() {
        // 测试有效手机号
        val validPhones = listOf("13812345678", "15987654321", "18666666666")
        validPhones.forEach { phone ->
            val result = ValidationUtils.validatePhoneNumber(phone)
            assertTrue("Phone $phone should be valid", result.isValid)
        }

        // 测试无效手机号
        val invalidPhones = listOf("", "12345678901", "1381234567", "23812345678", "1381234567a")
        invalidPhones.forEach { phone ->
            val result = ValidationUtils.validatePhoneNumber(phone)
            assertFalse("Phone $phone should be invalid", result.isValid)
        }
    }

    @Test
    fun testValidationResult() {
        // 测试有效结果
        val validResult = ValidationResult(true, emptyList())
        assertTrue(validResult.isValid)
        assertEquals("", validResult.getErrorMessage())
        assertNull(validResult.getFirstError())

        // 测试无效结果
        val errors = listOf("错误1", "错误2")
        val invalidResult = ValidationResult(false, errors)
        assertFalse(invalidResult.isValid)
        assertEquals("错误1\n错误2", invalidResult.getErrorMessage())
        assertEquals("错误1", invalidResult.getFirstError())
    }
}
