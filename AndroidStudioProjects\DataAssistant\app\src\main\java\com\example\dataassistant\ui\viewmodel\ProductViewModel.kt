package com.example.dataassistant.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.repository.ProductRepository
import com.example.dataassistant.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 商品管理ViewModel
 */
@HiltViewModel
class ProductViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {

    private val _uiState = MutableLiveData<ProductUiState>()
    val uiState: LiveData<ProductUiState> = _uiState

    private val _selectedProduct = MutableLiveData<Product?>()
    val selectedProduct: LiveData<Product?> = _selectedProduct

    // 商品列表数据
    val allProducts = productRepository.getAllProducts()
    val normalProducts = productRepository.getProductsByStatus(ProductStatus.NORMAL)
    val warningProducts = productRepository.getProductsByStatus(ProductStatus.WARNING)
    val expiredProducts = productRepository.getProductsByStatus(ProductStatus.EXPIRED)

    init {
        _uiState.value = ProductUiState()
    }

    /**
     * 获取即将过期的商品
     */
    fun getExpiringProducts(days: Int = 7) = productRepository.getExpiringProducts(days)

    /**
     * 根据分类获取商品
     */
    fun getProductsByCategory(category: String) = productRepository.getProductsByCategory(category)

    /**
     * 根据ID获取商品
     */
    suspend fun getProductById(productId: String) = productRepository.getProductById(productId)

    /**
     * 搜索商品
     */
    fun searchProducts(keyword: String) = productRepository.searchProducts(keyword)

    /**
     * 添加商品
     */
    fun addProduct(product: Product) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)

                // 验证商品数据
                val validationResult = ValidationUtils.validateProduct(product)
                if (!validationResult.isValid) {
                    _uiState.value = _uiState.value?.copy(
                        isLoading = false,
                        error = validationResult.getErrorMessage()
                    )
                    return@launch
                }

                productRepository.insertProduct(product)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "商品添加成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "添加商品失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新商品
     */
    fun updateProduct(product: Product) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)

                // 验证商品数据
                val validationResult = ValidationUtils.validateProduct(product)
                if (!validationResult.isValid) {
                    _uiState.value = _uiState.value?.copy(
                        isLoading = false,
                        error = validationResult.getErrorMessage()
                    )
                    return@launch
                }

                productRepository.updateProduct(product)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "商品更新成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "更新商品失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 删除商品
     */
    fun deleteProduct(product: Product) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                productRepository.deleteProduct(product)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "商品删除成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "删除商品失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 批量删除商品
     */
    fun deleteProducts(productIds: List<String>) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                productRepository.deleteProductsByIds(productIds)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "批量删除成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "批量删除失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新商品状态
     */
    fun updateProductStatus(productId: String, status: ProductStatus) {
        viewModelScope.launch {
            try {
                productRepository.updateProductStatus(productId, status)
                _uiState.value = _uiState.value?.copy(message = "状态更新成功")
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "状态更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取商品统计信息
     */
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                val totalCount = productRepository.getTotalCount()
                val normalCount = productRepository.getCountByStatus(ProductStatus.NORMAL)
                val warningCount = productRepository.getCountByStatus(ProductStatus.WARNING)
                val expiredCount = productRepository.getCountByStatus(ProductStatus.EXPIRED)

                val statistics = ProductStatistics(
                    totalCount = totalCount,
                    normalCount = normalCount,
                    warningCount = warningCount,
                    expiredCount = expiredCount
                )

                _uiState.value = _uiState.value?.copy(statistics = statistics)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "加载统计信息失败: ${e.message}"
                )
            }
        }
    }



    /**
     * 删除所有商品
     */
    fun deleteAllProducts() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                productRepository.deleteAllProducts()
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "所有商品已删除"
                )
                loadStatistics()
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "删除失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 选择商品
     */
    fun selectProduct(product: Product?) {
        _selectedProduct.value = product
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value?.copy(error = null)
    }

    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value?.copy(message = null)
    }
}

/**
 * 商品UI状态
 */
data class ProductUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val statistics: ProductStatistics? = null
)

/**
 * 商品统计信息
 */
data class ProductStatistics(
    val totalCount: Int = 0,
    val normalCount: Int = 0,
    val warningCount: Int = 0,
    val expiredCount: Int = 0
)
