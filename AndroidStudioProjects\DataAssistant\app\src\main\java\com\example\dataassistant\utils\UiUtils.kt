package com.example.dataassistant.utils

import android.content.Context
import android.content.res.Configuration
import android.graphics.Color
import android.os.Build
import android.view.View
import android.view.inputmethod.InputMethodManager
import android.widget.Toast
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import com.google.android.material.snackbar.Snackbar

/**
 * UI工具类
 */
object UiUtils {

    /**
     * 显示Toast消息
     */
    fun showToast(context: Context, message: String, duration: Int = Toast.LENGTH_SHORT) {
        Toast.makeText(context, message, duration).show()
    }

    /**
     * 显示Snackbar消息
     */
    fun showSnackbar(view: View, message: String, duration: Int = Snackbar.LENGTH_SHORT) {
        Snackbar.make(view, message, duration).show()
    }

    /**
     * 显示带操作的Snackbar
     */
    fun showSnackbarWithAction(
        view: View,
        message: String,
        actionText: String,
        action: () -> Unit,
        duration: Int = Snackbar.LENGTH_LONG
    ) {
        Snackbar.make(view, message, duration)
            .setAction(actionText) { action() }
            .show()
    }

    /**
     * 隐藏软键盘
     */
    fun hideKeyboard(context: Context, view: View) {
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.hideSoftInputFromWindow(view.windowToken, 0)
    }

    /**
     * 显示软键盘
     */
    fun showKeyboard(context: Context, view: View) {
        val imm = context.getSystemService(Context.INPUT_METHOD_SERVICE) as InputMethodManager
        imm.showSoftInput(view, InputMethodManager.SHOW_IMPLICIT)
    }

    /**
     * 检查是否为深色模式
     */
    fun isDarkMode(context: Context): Boolean {
        val nightModeFlags = context.resources.configuration.uiMode and Configuration.UI_MODE_NIGHT_MASK
        return nightModeFlags == Configuration.UI_MODE_NIGHT_YES
    }

    /**
     * 获取状态栏高度
     */
    fun getStatusBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("status_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 获取导航栏高度
     */
    fun getNavigationBarHeight(context: Context): Int {
        var result = 0
        val resourceId = context.resources.getIdentifier("navigation_bar_height", "dimen", "android")
        if (resourceId > 0) {
            result = context.resources.getDimensionPixelSize(resourceId)
        }
        return result
    }

    /**
     * 设置状态栏颜色
     */
    fun setStatusBarColor(context: Context, view: View, colorResId: Int) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val window = (context as? androidx.appcompat.app.AppCompatActivity)?.window
            window?.statusBarColor = ContextCompat.getColor(context, colorResId)
        }
    }

    /**
     * 设置状态栏为透明
     */
    fun setStatusBarTransparent(context: Context) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            val window = (context as? androidx.appcompat.app.AppCompatActivity)?.window
            window?.statusBarColor = Color.TRANSPARENT
        }
    }

    /**
     * 设置全屏模式
     */
    fun setFullScreen(context: Context, enable: Boolean) {
        val window = (context as? androidx.appcompat.app.AppCompatActivity)?.window
        if (enable) {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window?.setDecorFitsSystemWindows(false)
            } else {
                @Suppress("DEPRECATION")
                window?.decorView?.systemUiVisibility = (
                    View.SYSTEM_UI_FLAG_LAYOUT_STABLE
                    or View.SYSTEM_UI_FLAG_LAYOUT_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_LAYOUT_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_HIDE_NAVIGATION
                    or View.SYSTEM_UI_FLAG_FULLSCREEN
                    or View.SYSTEM_UI_FLAG_IMMERSIVE_STICKY
                )
            }
        } else {
            if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
                window?.setDecorFitsSystemWindows(true)
            } else {
                @Suppress("DEPRECATION")
                window?.decorView?.systemUiVisibility = View.SYSTEM_UI_FLAG_VISIBLE
            }
        }
    }

    /**
     * dp转px
     */
    fun dpToPx(context: Context, dp: Float): Int {
        val density = context.resources.displayMetrics.density
        return (dp * density + 0.5f).toInt()
    }

    /**
     * px转dp
     */
    fun pxToDp(context: Context, px: Float): Int {
        val density = context.resources.displayMetrics.density
        return (px / density + 0.5f).toInt()
    }

    /**
     * sp转px
     */
    fun spToPx(context: Context, sp: Float): Int {
        val scaledDensity = context.resources.displayMetrics.scaledDensity
        return (sp * scaledDensity + 0.5f).toInt()
    }

    /**
     * 获取屏幕宽度
     */
    fun getScreenWidth(context: Context): Int {
        return context.resources.displayMetrics.widthPixels
    }

    /**
     * 获取屏幕高度
     */
    fun getScreenHeight(context: Context): Int {
        return context.resources.displayMetrics.heightPixels
    }

    /**
     * 检查是否为平板
     */
    fun isTablet(context: Context): Boolean {
        return (context.resources.configuration.screenLayout and Configuration.SCREENLAYOUT_SIZE_MASK) >= Configuration.SCREENLAYOUT_SIZE_LARGE
    }

    /**
     * 设置View的可见性（带动画）
     */
    fun setVisibilityWithAnimation(view: View, visibility: Int, duration: Long = 300) {
        when (visibility) {
            View.VISIBLE -> {
                if (view.visibility != View.VISIBLE) {
                    AnimationUtils.fadeIn(view, duration)
                }
            }
            View.GONE, View.INVISIBLE -> {
                if (view.visibility == View.VISIBLE) {
                    AnimationUtils.fadeOut(view, duration)
                } else {
                    view.visibility = visibility
                }
            }
        }
    }

    /**
     * 设置View的启用状态（带动画）
     */
    fun setEnabledWithAnimation(view: View, enabled: Boolean, duration: Long = 200) {
        view.isEnabled = enabled
        view.animate()
            .alpha(if (enabled) 1.0f else 0.5f)
            .setDuration(duration)
            .start()
    }

    /**
     * 获取颜色（兼容不同API版本）
     */
    fun getColor(context: Context, colorResId: Int): Int {
        return ContextCompat.getColor(context, colorResId)
    }

    /**
     * 获取Drawable（兼容不同API版本）
     */
    fun getDrawable(context: Context, drawableResId: Int) = ContextCompat.getDrawable(context, drawableResId)

    /**
     * 设置View的圆角
     */
    fun setRoundCorners(view: View, radius: Float) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.LOLLIPOP) {
            view.clipToOutline = true
            view.outlineProvider = object : android.view.ViewOutlineProvider() {
                override fun getOutline(view: View, outline: android.graphics.Outline) {
                    outline.setRoundRect(0, 0, view.width, view.height, radius)
                }
            }
        }
    }

    /**
     * 震动反馈
     */
    fun performHapticFeedback(view: View) {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.R) {
            view.performHapticFeedback(android.view.HapticFeedbackConstants.CONFIRM)
        } else {
            view.performHapticFeedback(android.view.HapticFeedbackConstants.VIRTUAL_KEY)
        }
    }

    /**
     * 为Fragment显示Toast
     */
    fun Fragment.showToast(message: String, duration: Int = Toast.LENGTH_SHORT) {
        context?.let { showToast(it, message, duration) }
    }

    /**
     * 为Fragment显示Snackbar
     */
    fun Fragment.showSnackbar(message: String, duration: Int = Snackbar.LENGTH_SHORT) {
        view?.let { showSnackbar(it, message, duration) }
    }

    /**
     * 为Fragment隐藏键盘
     */
    fun Fragment.hideKeyboard() {
        view?.let { v ->
            context?.let { ctx ->
                hideKeyboard(ctx, v)
            }
        }
    }
}
