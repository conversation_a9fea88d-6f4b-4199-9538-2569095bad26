package com.example.dataassistant.data.entity

import android.os.Parcelable
import androidx.room.Entity
import androidx.room.PrimaryKey
import kotlinx.parcelize.Parcelize
import java.util.*

/**
 * 保质期配置实体类
 */
@Entity(tableName = "shelf_life_configs")
@Parcelize
data class ShelfLifeConfig(
    @PrimaryKey
    val id: String = UUID.randomUUID().toString(),

    // 配置基本信息
    val name: String,                    // 配置名称
    val description: String? = null,     // 配置描述

    // 保质期范围
    val minShelfLifeDays: Int,           // 最小保质期天数
    val maxShelfLifeDays: Int,           // 最大保质期天数
    val shelfLifeUnit: String = "day",   // 保质期单位

    // 提醒配置
    val warningDays: Int = 3,            // 临期提醒天数
    val removalDays: Int = 1,            // 下架提醒天数
    val discountDays: Int = 5,           // 折扣提醒天数

    // 通知配置
    val enableNotification: Boolean = true,      // 是否启用通知
    val notificationTime: String = "09:00",      // 通知时间
    val notificationFrequency: Int = 1,          // 通知频率（天）

    // 状态
    val isEnabled: Boolean = true,       // 是否启用
    val isDefault: Boolean = false,      // 是否为默认配置

    // 系统信息
    val createTime: Long = System.currentTimeMillis(),
    val updateTime: Long = System.currentTimeMillis()
) : Parcelable

/**
 * 保质期单位枚举
 */
enum class ShelfLifeUnit(val value: String, val displayName: String, val days: Int) {
    DAY("day", "天", 1),
    WEEK("week", "周", 7),
    MONTH("month", "月", 30),
    YEAR("year", "年", 365);

    companion object {
        fun fromValue(value: String): ShelfLifeUnit {
            return values().find { it.value == value } ?: DAY
        }
    }
}
