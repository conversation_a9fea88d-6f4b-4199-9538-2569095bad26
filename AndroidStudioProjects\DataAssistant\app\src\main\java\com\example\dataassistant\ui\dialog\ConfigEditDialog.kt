package com.example.dataassistant.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.DialogFragment
import com.example.dataassistant.data.entity.ShelfLifeConfig
import com.example.dataassistant.databinding.DialogConfigEditBinding
import com.example.dataassistant.utils.ValidationUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.TimeFormat
import java.util.*

/**
 * 配置编辑对话框
 */
class ConfigEditDialog : DialogFragment() {

    private var _binding: DialogConfigEditBinding? = null
    private val binding get() = _binding!!

    private var editingConfig: ShelfLifeConfig? = null
    private var onConfigSavedListener: ((ShelfLifeConfig) -> Unit)? = null

    companion object {
        private const val ARG_CONFIG = "config"

        fun newInstance(config: ShelfLifeConfig? = null): ConfigEditDialog {
            val dialog = ConfigEditDialog()
            if (config != null) {
                val args = Bundle()
                args.putParcelable(ARG_CONFIG, config)
                dialog.arguments = args
            }
            return dialog
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        editingConfig = arguments?.getParcelable(ARG_CONFIG)
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogConfigEditBinding.inflate(layoutInflater)

        val title = if (editingConfig != null) "编辑配置" else "添加配置"

        val dialog = MaterialAlertDialogBuilder(requireContext())
            .setTitle(title)
            .setView(binding.root)
            .setPositiveButton("保存", null) // 设为null，稍后手动处理
            .setNegativeButton("取消", null)
            .create()

        // 手动处理保存按钮点击，避免对话框自动关闭
        dialog.setOnShowListener {
            dialog.getButton(android.app.AlertDialog.BUTTON_POSITIVE).setOnClickListener {
                if (validateAndSave()) {
                    dialog.dismiss()
                }
            }
        }

        setupUI()
        fillFormIfEditing()

        return dialog
    }

    private fun setupUI() {
        // 设置通知时间选择
        binding.editTextNotificationTime.setOnClickListener {
            showTimePicker()
        }

        // 设置默认值
        if (editingConfig == null) {
            setDefaultValues()
        }
    }

    private fun setDefaultValues() {
        binding.apply {
            editTextMinShelfLifeDays.setText("1")
            editTextMaxShelfLifeDays.setText("365")
            editTextWarningDays.setText("3")
            editTextRemovalDays.setText("1")
            editTextDiscountDays.setText("7")
            editTextNotificationFrequency.setText("24")
            editTextNotificationTime.setText("09:00")
            switchEnabled.isChecked = true
            switchNotificationEnabled.isChecked = true
        }
    }

    private fun fillFormIfEditing() {
        editingConfig?.let { config ->
            binding.apply {
                editTextConfigName.setText(config.name)
                editTextConfigDescription.setText(config.description ?: "")
                editTextMinShelfLifeDays.setText(config.minShelfLifeDays.toString())
                editTextMaxShelfLifeDays.setText(config.maxShelfLifeDays.toString())
                editTextWarningDays.setText(config.warningDays.toString())
                editTextRemovalDays.setText(config.removalDays.toString())
                editTextDiscountDays.setText(config.discountDays.toString())
                editTextNotificationFrequency.setText(config.notificationFrequency.toString())
                editTextNotificationTime.setText(config.notificationTime)
                switchEnabled.isChecked = config.isEnabled
                switchNotificationEnabled.isChecked = config.enableNotification
            }
        }
    }

    private fun showTimePicker() {
        val currentTime = binding.editTextNotificationTime.text.toString()
        val timeParts = currentTime.split(":")
        val hour = if (timeParts.size >= 2) timeParts[0].toIntOrNull() ?: 9 else 9
        val minute = if (timeParts.size >= 2) timeParts[1].toIntOrNull() ?: 0 else 0

        val timePicker = MaterialTimePicker.Builder()
            .setTimeFormat(TimeFormat.CLOCK_24H)
            .setHour(hour)
            .setMinute(minute)
            .setTitleText("选择通知时间")
            .build()

        timePicker.addOnPositiveButtonClickListener {
            val selectedTime = String.format("%02d:%02d", timePicker.hour, timePicker.minute)
            binding.editTextNotificationTime.setText(selectedTime)
        }

        timePicker.show(parentFragmentManager, "TIME_PICKER")
    }

    private fun validateAndSave(): Boolean {
        try {
            // 收集表单数据
            val name = binding.editTextConfigName.text.toString().trim()
            val description = binding.editTextConfigDescription.text.toString().trim().takeIf { it.isNotEmpty() }
            val minShelfLifeDaysStr = binding.editTextMinShelfLifeDays.text.toString().trim()
            val maxShelfLifeDaysStr = binding.editTextMaxShelfLifeDays.text.toString().trim()
            val warningDaysStr = binding.editTextWarningDays.text.toString().trim()
            val removalDaysStr = binding.editTextRemovalDays.text.toString().trim()
            val discountDaysStr = binding.editTextDiscountDays.text.toString().trim()
            val notificationFrequencyStr = binding.editTextNotificationFrequency.text.toString().trim()
            val notificationTime = binding.editTextNotificationTime.text.toString().trim()
            val isEnabled = binding.switchEnabled.isChecked
            val isNotificationEnabled = binding.switchNotificationEnabled.isChecked

            // 基本验证
            if (name.isEmpty()) {
                binding.editTextConfigName.error = "配置名称不能为空"
                return false
            }

            val minShelfLifeDays = minShelfLifeDaysStr.toIntOrNull() ?: run {
                binding.editTextMinShelfLifeDays.error = "请输入有效的数字"
                return false
            }

            val maxShelfLifeDays = maxShelfLifeDaysStr.toIntOrNull() ?: run {
                binding.editTextMaxShelfLifeDays.error = "请输入有效的数字"
                return false
            }

            val warningDays = warningDaysStr.toIntOrNull() ?: run {
                binding.editTextWarningDays.error = "请输入有效的数字"
                return false
            }

            val removalDays = removalDaysStr.toIntOrNull() ?: run {
                binding.editTextRemovalDays.error = "请输入有效的数字"
                return false
            }

            val discountDays = discountDaysStr.toIntOrNull() ?: run {
                binding.editTextDiscountDays.error = "请输入有效的数字"
                return false
            }

            val notificationFrequency = notificationFrequencyStr.toIntOrNull() ?: run {
                binding.editTextNotificationFrequency.error = "请输入有效的数字"
                return false
            }

            // 创建配置对象
            val config = if (editingConfig != null) {
                editingConfig!!.copy(
                    name = name,
                    description = description,
                    minShelfLifeDays = minShelfLifeDays,
                    maxShelfLifeDays = maxShelfLifeDays,
                    warningDays = warningDays,
                    removalDays = removalDays,
                    discountDays = discountDays,
                    notificationFrequency = notificationFrequency,
                    notificationTime = notificationTime,
                    isEnabled = isEnabled,
                    enableNotification = isNotificationEnabled,
                    updateTime = System.currentTimeMillis()
                )
            } else {
                ShelfLifeConfig(
                    id = UUID.randomUUID().toString(),
                    name = name,
                    description = description,
                    minShelfLifeDays = minShelfLifeDays,
                    maxShelfLifeDays = maxShelfLifeDays,
                    warningDays = warningDays,
                    removalDays = removalDays,
                    discountDays = discountDays,
                    notificationFrequency = notificationFrequency,
                    notificationTime = notificationTime,
                    isEnabled = isEnabled,
                    enableNotification = isNotificationEnabled,
                    createTime = System.currentTimeMillis(),
                    updateTime = System.currentTimeMillis()
                )
            }

            // 验证配置
            val validationResult = ValidationUtils.validateShelfLifeConfig(config)
            if (!validationResult.isValid) {
                Snackbar.make(binding.root, validationResult.getErrorMessage(), Snackbar.LENGTH_LONG).show()
                return false
            }

            // 保存配置
            onConfigSavedListener?.invoke(config)
            return true

        } catch (e: Exception) {
            Snackbar.make(binding.root, "保存失败: ${e.message}", Snackbar.LENGTH_LONG).show()
            return false
        }
    }

    fun setOnConfigSavedListener(listener: (ShelfLifeConfig) -> Unit) {
        onConfigSavedListener = listener
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
