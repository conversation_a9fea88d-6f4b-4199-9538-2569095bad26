package com.example.dataassistant.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.dataassistant.databinding.FragmentHomeBinding
import com.example.dataassistant.ui.adapter.ProductAdapter
import com.example.dataassistant.ui.dialog.DateCalculatorDialog
import com.example.dataassistant.ui.viewmodel.MainViewModel
import com.example.dataassistant.utils.DateUtils
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch

/**
 * 首页Fragment
 */
@AndroidEntryPoint
class HomeFragment : Fragment() {

    private var _binding: FragmentHomeBinding? = null
    private val binding get() = _binding!!

    private val viewModel: MainViewModel by viewModels()
    private lateinit var recentProductsAdapter: ProductAdapter

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        android.util.Log.d("HomeFragment", "onCreateView called")
        _binding = FragmentHomeBinding.inflate(inflater, container, false)
        android.util.Log.d("HomeFragment", "onCreateView completed")
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        android.util.Log.d("HomeFragment", "onViewCreated called")

        setupRecyclerView()
        setupObservers()
        setupClickListeners()

        // 加载数据
        viewModel.loadHomeData()

        // 添加测试内容以验证UI是否可见
        binding.textViewTotalCount.text = "5"
        binding.textViewWarningCount.text = "2"
        binding.textViewExpiredCount.text = "1"
        binding.textViewHealthPercentage.text = "80%"
        binding.progressBarHealth.progress = 80

        android.util.Log.d("HomeFragment", "Test data set")
        android.util.Log.d("HomeFragment", "onViewCreated completed")
    }

    private fun setupRecyclerView() {
        recentProductsAdapter = ProductAdapter(
            onItemClick = { product ->
                // 点击商品项的处理
                // TODO: 导航到商品详情页面
            }
        )

        binding.recyclerViewRecentProducts.apply {
            layoutManager = LinearLayoutManager(context)
            adapter = recentProductsAdapter
        }
    }

    private fun setupObservers() {
        // 观察UI状态
        viewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            binding.progressBar.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE

            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }

            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                viewModel.clearMessage()
            }
        }

        // 观察统计信息
        viewModel.statistics.observe(viewLifecycleOwner) { statistics ->
            updateStatistics(statistics)
        }

        // 观察最近商品
        viewModel.recentProducts.observe(viewLifecycleOwner) { products ->
            recentProductsAdapter.submitList(products)

            // 更新空状态
            if (products.isEmpty()) {
                binding.textViewEmptyState.visibility = View.VISIBLE
                binding.recyclerViewRecentProducts.visibility = View.GONE
            } else {
                binding.textViewEmptyState.visibility = View.GONE
                binding.recyclerViewRecentProducts.visibility = View.VISIBLE
            }
        }

        // 观察即将过期的商品
        viewModel.getExpiringProducts(7).observe(viewLifecycleOwner) { expiringProducts ->
            updateExpiringProductsAlert(expiringProducts.size)
        }
    }

    private fun setupClickListeners() {
        // 刷新按钮
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.refreshData()
            binding.swipeRefreshLayout.isRefreshing = false
        }

        // 日期计算器按钮
        binding.buttonDateCalculator.setOnClickListener {
            showDateCalculator()
        }

        // 统计卡片点击事件
        binding.cardTotalProducts.setOnClickListener {
            // TODO: 导航到商品列表页面
        }

        binding.cardWarningProducts.setOnClickListener {
            // TODO: 导航到临期商品列表
        }

        binding.cardExpiredProducts.setOnClickListener {
            // TODO: 导航到过期商品列表
        }

        // 快捷操作按钮
        binding.buttonAddProduct.setOnClickListener {
            // TODO: 导航到添加商品页面
        }

        binding.buttonViewList.setOnClickListener {
            // TODO: 导航到商品列表页面
        }

        // 查看更多按钮
        binding.buttonViewMore.setOnClickListener {
            // TODO: 导航到完整商品列表
        }
    }

    private fun showDateCalculator() {
        val dialog = DateCalculatorDialog.newInstance()
        dialog.show(parentFragmentManager, "DATE_CALCULATOR_DIALOG")
    }

    private fun updateStatistics(statistics: com.example.dataassistant.ui.viewmodel.HomeStatistics) {
        binding.apply {
            textViewTotalCount.text = statistics.totalProducts.toString()
            textViewWarningCount.text = statistics.warningProducts.toString()
            textViewExpiredCount.text = statistics.expiredProducts.toString()

            // 更新健康度
            val healthPercentage = statistics.getHealthPercentage()
            textViewHealthPercentage.text = "${healthPercentage.toInt()}%"
            progressBarHealth.progress = healthPercentage.toInt()

            // 更新风险等级颜色
            val riskLevel = statistics.getRiskLevel()
            val color = when (riskLevel) {
                com.example.dataassistant.ui.viewmodel.RiskLevel.LOW ->
                    requireContext().getColor(android.R.color.holo_green_light)
                com.example.dataassistant.ui.viewmodel.RiskLevel.MEDIUM ->
                    requireContext().getColor(android.R.color.holo_orange_light)
                com.example.dataassistant.ui.viewmodel.RiskLevel.HIGH ->
                    requireContext().getColor(android.R.color.holo_red_light)
            }
            progressBarHealth.progressTintList = android.content.res.ColorStateList.valueOf(color)
        }
    }

    private fun updateExpiringProductsAlert(count: Int) {
        if (count > 0) {
            binding.cardAlert.visibility = View.VISIBLE
            binding.textViewAlertMessage.text = "您有 $count 件商品即将在7天内过期"
        } else {
            binding.cardAlert.visibility = View.GONE
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
