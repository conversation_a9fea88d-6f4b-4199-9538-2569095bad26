package com.example.dataassistant.utils

import android.animation.Animator
import android.animation.AnimatorListenerAdapter
import android.animation.ObjectAnimator
import android.animation.ValueAnimator
import android.view.View
import android.view.animation.AccelerateDecelerateInterpolator
import android.view.animation.Animation
import android.view.animation.AnimationUtils as AndroidAnimationUtils
import androidx.interpolator.view.animation.FastOutSlowInInterpolator
import com.example.dataassistant.R

/**
 * 动画工具类
 */
object AnimationUtils {

    /**
     * 淡入动画
     */
    fun fadeIn(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.alpha = 0f
        view.visibility = View.VISIBLE
        
        ObjectAnimator.ofFloat(view, "alpha", 0f, 1f).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    onEnd?.invoke()
                }
            })
            start()
        }
    }

    /**
     * 淡出动画
     */
    fun fadeOut(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        ObjectAnimator.ofFloat(view, "alpha", 1f, 0f).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    onEnd?.invoke()
                }
            })
            start()
        }
    }

    /**
     * 缩放动画
     */
    fun scaleIn(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.scaleX = 0f
        view.scaleY = 0f
        view.visibility = View.VISIBLE
        
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 0f, 1f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 0f, 1f)
        
        scaleX.duration = duration
        scaleY.duration = duration
        scaleX.interpolator = FastOutSlowInInterpolator()
        scaleY.interpolator = FastOutSlowInInterpolator()
        
        scaleX.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                onEnd?.invoke()
            }
        })
        
        scaleX.start()
        scaleY.start()
    }

    /**
     * 缩放出动画
     */
    fun scaleOut(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        val scaleX = ObjectAnimator.ofFloat(view, "scaleX", 1f, 0f)
        val scaleY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 0f)
        
        scaleX.duration = duration
        scaleY.duration = duration
        scaleX.interpolator = FastOutSlowInInterpolator()
        scaleY.interpolator = FastOutSlowInInterpolator()
        
        scaleX.addListener(object : AnimatorListenerAdapter() {
            override fun onAnimationEnd(animation: Animator) {
                view.visibility = View.GONE
                onEnd?.invoke()
            }
        })
        
        scaleX.start()
        scaleY.start()
    }

    /**
     * 滑入动画
     */
    fun slideInFromRight(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        view.translationX = view.width.toFloat()
        view.visibility = View.VISIBLE
        
        ObjectAnimator.ofFloat(view, "translationX", view.width.toFloat(), 0f).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    onEnd?.invoke()
                }
            })
            start()
        }
    }

    /**
     * 滑出动画
     */
    fun slideOutToLeft(view: View, duration: Long = 300, onEnd: (() -> Unit)? = null) {
        ObjectAnimator.ofFloat(view, "translationX", 0f, -view.width.toFloat()).apply {
            this.duration = duration
            interpolator = FastOutSlowInInterpolator()
            addListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    view.visibility = View.GONE
                    onEnd?.invoke()
                }
            })
            start()
        }
    }

    /**
     * 弹跳动画
     */
    fun bounce(view: View, duration: Long = 600) {
        val animator = ObjectAnimator.ofFloat(view, "scaleX", 1f, 1.2f, 1f)
        val animatorY = ObjectAnimator.ofFloat(view, "scaleY", 1f, 1.2f, 1f)
        
        animator.duration = duration
        animatorY.duration = duration
        animator.interpolator = AccelerateDecelerateInterpolator()
        animatorY.interpolator = AccelerateDecelerateInterpolator()
        
        animator.start()
        animatorY.start()
    }

    /**
     * 摇摆动画
     */
    fun shake(view: View, duration: Long = 500) {
        val animator = ObjectAnimator.ofFloat(view, "translationX", 0f, 25f, -25f, 25f, -25f, 15f, -15f, 6f, -6f, 0f)
        animator.duration = duration
        animator.start()
    }

    /**
     * 旋转动画
     */
    fun rotate(view: View, fromDegrees: Float = 0f, toDegrees: Float = 360f, duration: Long = 1000) {
        val animator = ObjectAnimator.ofFloat(view, "rotation", fromDegrees, toDegrees)
        animator.duration = duration
        animator.repeatCount = ValueAnimator.INFINITE
        animator.start()
    }

    /**
     * 停止旋转动画
     */
    fun stopRotation(view: View) {
        view.animate().rotation(0f).setDuration(300).start()
    }

    /**
     * 加载XML动画
     */
    fun loadAnimation(view: View, animationResId: Int): Animation {
        return AndroidAnimationUtils.loadAnimation(view.context, animationResId)
    }

    /**
     * 列表项动画
     */
    fun animateListItem(view: View, position: Int, duration: Long = 300) {
        view.alpha = 0f
        view.translationY = 50f
        
        view.animate()
            .alpha(1f)
            .translationY(0f)
            .setDuration(duration)
            .setStartDelay((position * 50).toLong())
            .setInterpolator(FastOutSlowInInterpolator())
            .start()
    }

    /**
     * 卡片翻转动画
     */
    fun flipCard(frontView: View, backView: View, duration: Long = 600) {
        val halfDuration = duration / 2
        
        // 翻转前半部分
        frontView.animate()
            .rotationY(90f)
            .setDuration(halfDuration)
            .setListener(object : AnimatorListenerAdapter() {
                override fun onAnimationEnd(animation: Animator) {
                    frontView.visibility = View.GONE
                    backView.visibility = View.VISIBLE
                    backView.rotationY = -90f
                    
                    // 翻转后半部分
                    backView.animate()
                        .rotationY(0f)
                        .setDuration(halfDuration)
                        .setListener(null)
                        .start()
                }
            })
            .start()
    }
}
