package com.example.dataassistant.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.dataassistant.data.entity.ShelfLifeConfig
import com.example.dataassistant.data.repository.ShelfLifeConfigRepository
import com.example.dataassistant.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 配置管理ViewModel
 */
@HiltViewModel
class ConfigViewModel @Inject constructor(
    private val configRepository: ShelfLifeConfigRepository
) : ViewModel() {

    private val _uiState = MutableLiveData<ConfigUiState>()
    val uiState: LiveData<ConfigUiState> = _uiState

    private val _selectedConfig = MutableLiveData<ShelfLifeConfig?>()
    val selectedConfig: LiveData<ShelfLifeConfig?> = _selectedConfig

    // 配置列表数据
    val allConfigs = configRepository.getAllConfigs()
    val enabledConfigs = configRepository.getEnabledConfigs()

    init {
        _uiState.value = ConfigUiState()
        loadDefaultConfig()
    }

    /**
     * 加载默认配置
     */
    private fun loadDefaultConfig() {
        viewModelScope.launch {
            try {
                val defaultConfig = configRepository.getDefaultConfig()
                _uiState.value = _uiState.value?.copy(defaultConfig = defaultConfig)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "加载默认配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 添加配置
     */
    fun addConfig(config: ShelfLifeConfig) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)

                // 验证配置数据
                val validationResult = ValidationUtils.validateShelfLifeConfig(config)
                if (!validationResult.isValid) {
                    _uiState.value = _uiState.value?.copy(
                        isLoading = false,
                        error = validationResult.getErrorMessage()
                    )
                    return@launch
                }

                configRepository.insertConfig(config)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "配置添加成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "添加配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新配置
     */
    fun updateConfig(config: ShelfLifeConfig) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                configRepository.updateConfig(config)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "配置更新成功"
                )

                // 如果更新的是默认配置，重新加载
                if (config.isDefault) {
                    loadDefaultConfig()
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "更新配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 删除配置
     */
    fun deleteConfig(config: ShelfLifeConfig) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                configRepository.deleteConfig(config)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "配置删除成功"
                )

                // 如果删除的是默认配置，重新加载
                if (config.isDefault) {
                    loadDefaultConfig()
                }
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "删除配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 设置默认配置
     */
    fun setDefaultConfig(configId: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                configRepository.setDefaultConfig(configId)
                loadDefaultConfig()
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "默认配置设置成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "设置默认配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 启用/禁用配置
     */
    fun toggleConfigEnabled(configId: String, enabled: Boolean) {
        viewModelScope.launch {
            try {
                configRepository.updateConfigEnabled(configId, enabled)
                _uiState.value = _uiState.value?.copy(
                    message = if (enabled) "配置已启用" else "配置已禁用"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "更新配置状态失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新配置启用状态
     */
    fun updateConfigEnabled(configId: String, enabled: Boolean) {
        toggleConfigEnabled(configId, enabled)
    }

    /**
     * 加载配置列表
     */
    fun loadConfigs() {
        // 配置列表通过LiveData自动更新，这里可以用于刷新操作
        loadStatistics()
    }

    /**
     * 重置为默认配置
     */
    fun resetToDefaultConfigs() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                configRepository.deleteAllConfigs()
                configRepository.createDefaultConfigs()
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "已重置为默认配置"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "重置配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 搜索配置
     */
    fun searchConfigs(keyword: String) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isSearching = true)
                val results = configRepository.searchConfigs(keyword)
                _uiState.value = _uiState.value?.copy(
                    isSearching = false,
                    searchResults = results
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isSearching = false,
                    error = "搜索配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 创建默认配置
     */
    fun createDefaultConfigs() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                configRepository.createDefaultConfigs()
                loadDefaultConfig()
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "默认配置创建成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "创建默认配置失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取配置统计信息
     */
    fun loadStatistics() {
        viewModelScope.launch {
            try {
                val totalCount = configRepository.getConfigCount()
                _uiState.value = _uiState.value?.copy(
                    statistics = ConfigStatistics(totalCount = totalCount)
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "加载统计信息失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 选择配置
     */
    fun selectConfig(config: ShelfLifeConfig?) {
        _selectedConfig.value = config
    }

    /**
     * 清除搜索结果
     */
    fun clearSearchResults() {
        _uiState.value = _uiState.value?.copy(searchResults = emptyList())
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value?.copy(error = null)
    }

    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value?.copy(message = null)
    }
}

/**
 * 配置UI状态
 */
data class ConfigUiState(
    val isLoading: Boolean = false,
    val isSearching: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val defaultConfig: ShelfLifeConfig? = null,
    val searchResults: List<ShelfLifeConfig> = emptyList(),
    val statistics: ConfigStatistics? = null
)

/**
 * 配置统计信息
 */
data class ConfigStatistics(
    val totalCount: Int = 0
)
