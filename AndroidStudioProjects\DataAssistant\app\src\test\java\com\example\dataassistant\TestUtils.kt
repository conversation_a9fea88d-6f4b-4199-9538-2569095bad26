package com.example.dataassistant

import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.entity.ShelfLifeConfig
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 测试工具类
 */
object TestUtils {

    /**
     * 创建测试商品
     */
    fun createTestProduct(
        id: String = UUID.randomUUID().toString(),
        name: String = "测试商品",
        brand: String? = "测试品牌",
        category: String? = "测试分类",
        barcode: String? = "1234567890123",
        productionDate: Long = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(10),
        expiryDate: Long = System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30),
        stockDate: Long = System.currentTimeMillis(),
        quantity: Int = 1,
        shelfLifeDays: Int = 30,
        shelfLifeNumber: Int = 30,
        shelfLifeUnit: String = "天",
        warningDays: Int = 3,
        removalDays: Int = 1,
        status: ProductStatus = ProductStatus.NORMAL,
        notes: String? = null,
        location: String? = null,
        configId: String? = null,
        isNotificationEnabled: Boolean = true,
        createTime: Long = System.currentTimeMillis(),
        updateTime: Long = System.currentTimeMillis()
    ): Product {
        return Product(
            id = id,
            name = name,
            brand = brand,
            category = category,
            barcode = barcode,
            productionDate = productionDate,
            expiryDate = expiryDate,
            stockDate = stockDate,
            quantity = quantity,
            shelfLifeDays = shelfLifeDays,
            shelfLifeNumber = shelfLifeNumber,
            shelfLifeUnit = shelfLifeUnit,
            warningDays = warningDays,
            removalDays = removalDays,
            status = status,
            notes = notes,
            location = location,
            configId = configId,
            isNotificationEnabled = isNotificationEnabled,
            createTime = createTime,
            updateTime = updateTime
        )
    }

    /**
     * 创建测试配置
     */
    fun createTestConfig(
        id: String = UUID.randomUUID().toString(),
        name: String = "测试配置",
        description: String? = "测试配置描述",
        minShelfLifeDays: Int = 1,
        maxShelfLifeDays: Int = 365,
        warningDays: Int = 3,
        removalDays: Int = 1,
        discountDays: Int = 7,
        notificationFrequency: Int = 24,
        notificationTime: String = "09:00",
        isEnabled: Boolean = true,
        isNotificationEnabled: Boolean = true,
        isDefault: Boolean = false,
        createTime: Long = System.currentTimeMillis(),
        updateTime: Long = System.currentTimeMillis()
    ): ShelfLifeConfig {
        return ShelfLifeConfig(
            id = id,
            name = name,
            description = description,
            minShelfLifeDays = minShelfLifeDays,
            maxShelfLifeDays = maxShelfLifeDays,
            warningDays = warningDays,
            removalDays = removalDays,
            discountDays = discountDays,
            notificationFrequency = notificationFrequency,
            notificationTime = notificationTime,
            isEnabled = isEnabled,
            isNotificationEnabled = isNotificationEnabled,
            isDefault = isDefault,
            createTime = createTime,
            updateTime = updateTime
        )
    }

    /**
     * 创建临期商品
     */
    fun createExpiringProduct(daysUntilExpiry: Int): Product {
        val now = System.currentTimeMillis()
        return createTestProduct(
            name = "临期商品",
            expiryDate = now + TimeUnit.DAYS.toMillis(daysUntilExpiry.toLong()),
            warningDays = 7
        )
    }

    /**
     * 创建过期商品
     */
    fun createExpiredProduct(daysExpired: Int): Product {
        val now = System.currentTimeMillis()
        return createTestProduct(
            name = "过期商品",
            expiryDate = now - TimeUnit.DAYS.toMillis(daysExpired.toLong()),
            status = ProductStatus.EXPIRED
        )
    }

    /**
     * 创建正常商品
     */
    fun createNormalProduct(): Product {
        val now = System.currentTimeMillis()
        return createTestProduct(
            name = "正常商品",
            expiryDate = now + TimeUnit.DAYS.toMillis(30),
            status = ProductStatus.NORMAL
        )
    }

    /**
     * 创建商品列表
     */
    fun createProductList(count: Int): List<Product> {
        return (1..count).map { index ->
            createTestProduct(
                name = "商品$index",
                expiryDate = System.currentTimeMillis() + TimeUnit.DAYS.toMillis((index * 5).toLong())
            )
        }
    }

    /**
     * 创建配置列表
     */
    fun createConfigList(count: Int): List<ShelfLifeConfig> {
        return (1..count).map { index ->
            createTestConfig(
                name = "配置$index",
                warningDays = index * 2,
                isDefault = index == 1
            )
        }
    }

    /**
     * 断言商品状态
     */
    fun assertProductStatus(product: Product, expectedStatus: ProductStatus) {
        assert(product.status == expectedStatus) {
            "Expected status $expectedStatus but was ${product.status}"
        }
    }

    /**
     * 断言日期在范围内
     */
    fun assertDateInRange(date: Long, startDate: Long, endDate: Long) {
        assert(date in startDate..endDate) {
            "Date $date is not in range [$startDate, $endDate]"
        }
    }

    /**
     * 断言字符串不为空
     */
    fun assertNotEmpty(value: String?, fieldName: String = "Value") {
        assert(!value.isNullOrEmpty()) {
            "$fieldName should not be null or empty"
        }
    }

    /**
     * 断言数值在范围内
     */
    fun assertInRange(value: Int, min: Int, max: Int, fieldName: String = "Value") {
        assert(value in min..max) {
            "$fieldName $value is not in range [$min, $max]"
        }
    }

    /**
     * 模拟延迟
     */
    suspend fun delay(milliseconds: Long) {
        kotlinx.coroutines.delay(milliseconds)
    }

    /**
     * 获取测试时间戳
     */
    fun getTestTimestamp(year: Int, month: Int, day: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.set(year, month - 1, day, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    /**
     * 比较两个商品是否相等（忽略时间戳）
     */
    fun compareProductsIgnoreTimestamp(product1: Product, product2: Product): Boolean {
        return product1.copy(createTime = 0, updateTime = 0) == 
               product2.copy(createTime = 0, updateTime = 0)
    }

    /**
     * 比较两个配置是否相等（忽略时间戳）
     */
    fun compareConfigsIgnoreTimestamp(config1: ShelfLifeConfig, config2: ShelfLifeConfig): Boolean {
        return config1.copy(createTime = 0, updateTime = 0) == 
               config2.copy(createTime = 0, updateTime = 0)
    }
}
