<?xml version="1.0" encoding="utf-8"?>
<navigation xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/nav_graph"
    app:startDestination="@id/navigation_home">

    <!-- 首页 -->
    <fragment
        android:id="@+id/navigation_home"
        android:name="com.example.dataassistant.ui.fragment.HomeFragment"
        android:label="首页"
        tools:layout="@layout/fragment_home" />

    <!-- 添加商品 -->
    <fragment
        android:id="@+id/navigation_add"
        android:name="com.example.dataassistant.ui.fragment.AddProductFragment"
        android:label="添加商品"
        tools:layout="@layout/fragment_add_product" />

    <!-- 商品列表 -->
    <fragment
        android:id="@+id/navigation_list"
        android:name="com.example.dataassistant.ui.fragment.ProductListFragment"
        android:label="商品列表"
        tools:layout="@layout/fragment_product_list">

        <action
            android:id="@+id/action_product_list_to_product_detail"
            app:destination="@id/product_detail" />

        <action
            android:id="@+id/action_product_list_to_product_edit"
            app:destination="@id/product_edit" />
    </fragment>

    <!-- 配置管理 -->
    <fragment
        android:id="@+id/navigation_config"
        android:name="com.example.dataassistant.ui.fragment.ConfigFragment"
        android:label="配置管理"
        tools:layout="@layout/fragment_config" />

    <!-- 商品详情 -->
    <fragment
        android:id="@+id/product_detail"
        android:name="com.example.dataassistant.ui.fragment.ProductDetailFragment"
        android:label="商品详情"
        tools:layout="@layout/fragment_product_detail">

        <argument
            android:name="productId"
            app:argType="string" />

        <action
            android:id="@+id/action_product_detail_to_product_edit"
            app:destination="@id/product_edit" />
    </fragment>

    <!-- 商品编辑 -->
    <fragment
        android:id="@+id/product_edit"
        android:name="com.example.dataassistant.ui.fragment.ProductEditFragment"
        android:label="编辑商品"
        tools:layout="@layout/fragment_product_edit">

        <argument
            android:name="productId"
            app:argType="string" />
    </fragment>

</navigation>
