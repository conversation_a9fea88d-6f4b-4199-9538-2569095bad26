package com.example.dataassistant.utils

import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * 日期工具类
 */
object DateUtils {

    private const val DATE_FORMAT = "yyyy-MM-dd"
    private const val DATETIME_FORMAT = "yyyy-MM-dd HH:mm:ss"
    private const val TIME_FORMAT = "HH:mm"

    private val dateFormat = SimpleDateFormat(DATE_FORMAT, Locale.getDefault())
    private val dateTimeFormat = SimpleDateFormat(DATETIME_FORMAT, Locale.getDefault())
    private val timeFormat = SimpleDateFormat(TIME_FORMAT, Locale.getDefault())

    /**
     * 格式化日期
     */
    fun formatDate(timestamp: Long): String {
        return dateFormat.format(Date(timestamp))
    }

    /**
     * 格式化日期时间
     */
    fun formatDateTime(timestamp: Long): String {
        return dateTimeFormat.format(Date(timestamp))
    }

    /**
     * 格式化时间
     */
    fun formatTime(timestamp: Long): String {
        return timeFormat.format(Date(timestamp))
    }

    /**
     * 解析日期字符串
     */
    fun parseDate(dateString: String): Long? {
        return try {
            dateFormat.parse(dateString)?.time
        } catch (e: Exception) {
            null
        }
    }

    /**
     * 计算两个日期之间的天数差
     */
    fun daysBetween(startTime: Long, endTime: Long): Int {
        val diffInMillis = endTime - startTime
        return TimeUnit.MILLISECONDS.toDays(diffInMillis).toInt()
    }

    /**
     * 计算两个日期之间的天数差
     */
    fun daysBetween(startDate: Date, endDate: Date): Int {
        val diffInMillis = endDate.time - startDate.time
        return TimeUnit.MILLISECONDS.toDays(diffInMillis).toInt()
    }

    /**
     * 获取距离过期的天数
     */
    fun getDaysUntilExpiry(expiryDate: Long): Int {
        return daysBetween(System.currentTimeMillis(), expiryDate)
    }

    /**
     * 判断是否已过期
     */
    fun isExpired(expiryDate: Long): Boolean {
        return expiryDate < System.currentTimeMillis()
    }

    /**
     * 判断是否即将过期
     */
    fun isExpiringSoon(expiryDate: Long, warningDays: Int): Boolean {
        val daysUntilExpiry = getDaysUntilExpiry(expiryDate)
        return daysUntilExpiry in 0..warningDays
    }

    /**
     * 获取今天的开始时间戳
     */
    fun getTodayStart(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 0)
        calendar.set(Calendar.MINUTE, 0)
        calendar.set(Calendar.SECOND, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        return calendar.timeInMillis
    }

    /**
     * 获取今天的结束时间戳
     */
    fun getTodayEnd(): Long {
        val calendar = Calendar.getInstance()
        calendar.set(Calendar.HOUR_OF_DAY, 23)
        calendar.set(Calendar.MINUTE, 59)
        calendar.set(Calendar.SECOND, 59)
        calendar.set(Calendar.MILLISECOND, 999)
        return calendar.timeInMillis
    }

    /**
     * 添加天数
     */
    fun addDays(timestamp: Long, days: Int): Long {
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = timestamp
        calendar.add(Calendar.DAY_OF_YEAR, days)
        return calendar.timeInMillis
    }

    /**
     * 获取相对时间描述
     */
    fun getRelativeTimeString(timestamp: Long): String {
        val now = System.currentTimeMillis()
        val diff = now - timestamp

        return when {
            diff < TimeUnit.MINUTES.toMillis(1) -> "刚刚"
            diff < TimeUnit.HOURS.toMillis(1) -> "${TimeUnit.MILLISECONDS.toMinutes(diff)}分钟前"
            diff < TimeUnit.DAYS.toMillis(1) -> "${TimeUnit.MILLISECONDS.toHours(diff)}小时前"
            diff < TimeUnit.DAYS.toMillis(7) -> "${TimeUnit.MILLISECONDS.toDays(diff)}天前"
            else -> formatDate(timestamp)
        }
    }

    /**
     * 获取过期状态描述
     */
    fun getExpiryStatusText(expiryDate: Long, warningDays: Int = 3): String {
        val daysUntilExpiry = getDaysUntilExpiry(expiryDate)

        return when {
            daysUntilExpiry < 0 -> "已过期${Math.abs(daysUntilExpiry)}天"
            daysUntilExpiry == 0 -> "今天过期"
            daysUntilExpiry <= warningDays -> "还有${daysUntilExpiry}天过期"
            else -> "还有${daysUntilExpiry}天过期"
        }
    }

    /**
     * 格式化剩余时间
     */
    fun formatRemainingTime(remainingDays: Int): String {
        return when {
            remainingDays < 0 -> "已过期${Math.abs(remainingDays)}天"
            remainingDays == 0 -> "今天过期"
            remainingDays == 1 -> "明天过期"
            remainingDays <= 7 -> "还有${remainingDays}天"
            remainingDays <= 30 -> "还有${remainingDays}天"
            else -> "还有${remainingDays}天"
        }
    }
}
