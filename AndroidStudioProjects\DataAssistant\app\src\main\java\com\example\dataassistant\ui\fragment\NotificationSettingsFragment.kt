package com.example.dataassistant.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.dataassistant.databinding.FragmentNotificationSettingsBinding
import com.example.dataassistant.notification.NotificationManager
import com.example.dataassistant.ui.viewmodel.ConfigViewModel
import com.google.android.material.snackbar.Snackbar
import com.google.android.material.timepicker.MaterialTimePicker
import com.google.android.material.timepicker.TimeFormat
import kotlinx.coroutines.launch

/**
 * 通知设置Fragment
 */
class NotificationSettingsFragment : Fragment() {
    
    private var _binding: FragmentNotificationSettingsBinding? = null
    private val binding get() = _binding!!
    
    private val configViewModel: ConfigViewModel by viewModels()
    private lateinit var notificationManager: NotificationManager
    
    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentNotificationSettingsBinding.inflate(inflater, container, false)
        return binding.root
    }
    
    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)
        
        notificationManager = NotificationManager(requireContext())
        
        setupUI()
        setupObservers()
        setupClickListeners()
        
        loadCurrentSettings()
    }
    
    private fun setupUI() {
        // 设置默认值
        binding.apply {
            switchNotificationEnabled.isChecked = true
            editTextNotificationInterval.setText("24")
            editTextNotificationTime.setText("09:00")
        }
    }
    
    private fun setupObservers() {
        // 观察通知工作状态
        notificationManager.getNotificationWorkStatus().observe(viewLifecycleOwner) { workInfos ->
            val isRunning = workInfos.any { it.state.isFinished.not() }
            binding.textViewNotificationStatus.text = if (isRunning) "通知已启用" else "通知已禁用"
        }
        
        // 观察配置变化
        configViewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                configViewModel.clearError()
            }
            
            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                configViewModel.clearMessage()
            }
        }
    }
    
    private fun setupClickListeners() {
        // 通知开关
        binding.switchNotificationEnabled.setOnCheckedChangeListener { _, isChecked ->
            updateNotificationSettings()
        }
        
        // 通知时间选择
        binding.editTextNotificationTime.setOnClickListener {
            showTimePicker()
        }
        
        // 立即测试通知
        binding.buttonTestNotification.setOnClickListener {
            testNotification()
        }
        
        // 保存设置
        binding.buttonSaveSettings.setOnClickListener {
            saveSettings()
        }
        
        // 重置设置
        binding.buttonResetSettings.setOnClickListener {
            resetSettings()
        }
        
        // 通知间隔变化
        binding.editTextNotificationInterval.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                updateNotificationSettings()
            }
        }
    }
    
    private fun loadCurrentSettings() {
        // 从SharedPreferences加载设置
        val sharedPrefs = requireContext().getSharedPreferences("notification_settings", 0)
        
        binding.apply {
            switchNotificationEnabled.isChecked = sharedPrefs.getBoolean("enabled", true)
            editTextNotificationInterval.setText(sharedPrefs.getString("interval", "24"))
            editTextNotificationTime.setText(sharedPrefs.getString("time", "09:00"))
        }
        
        // 检查通知权限
        if (!notificationManager.hasNotificationPermission()) {
            binding.textViewPermissionWarning.visibility = View.VISIBLE
            binding.buttonRequestPermission.visibility = View.VISIBLE
            
            binding.buttonRequestPermission.setOnClickListener {
                notificationManager.requestNotificationPermission(requireActivity())
            }
        }
    }
    
    private fun showTimePicker() {
        val currentTime = binding.editTextNotificationTime.text.toString()
        val timeParts = currentTime.split(":")
        val hour = if (timeParts.size >= 2) timeParts[0].toIntOrNull() ?: 9 else 9
        val minute = if (timeParts.size >= 2) timeParts[1].toIntOrNull() ?: 0 else 0
        
        val timePicker = MaterialTimePicker.Builder()
            .setTimeFormat(TimeFormat.CLOCK_24H)
            .setHour(hour)
            .setMinute(minute)
            .setTitleText("选择通知时间")
            .build()
        
        timePicker.addOnPositiveButtonClickListener {
            val selectedTime = String.format("%02d:%02d", timePicker.hour, timePicker.minute)
            binding.editTextNotificationTime.setText(selectedTime)
            updateNotificationSettings()
        }
        
        timePicker.show(parentFragmentManager, "TIME_PICKER")
    }
    
    private fun updateNotificationSettings() {
        val enabled = binding.switchNotificationEnabled.isChecked
        val intervalStr = binding.editTextNotificationInterval.text.toString()
        val timeStr = binding.editTextNotificationTime.text.toString()
        
        val interval = intervalStr.toLongOrNull() ?: 24L
        val timeParts = timeStr.split(":")
        val hour = if (timeParts.size >= 2) timeParts[0].toIntOrNull() ?: 9 else 9
        val minute = if (timeParts.size >= 2) timeParts[1].toIntOrNull() ?: 0 else 0
        
        notificationManager.updateNotificationSettings(enabled, interval, hour, minute)
    }
    
    private fun testNotification() {
        notificationManager.triggerImmediateNotification()
        Snackbar.make(binding.root, "测试通知已发送", Snackbar.LENGTH_SHORT).show()
    }
    
    private fun saveSettings() {
        val sharedPrefs = requireContext().getSharedPreferences("notification_settings", 0)
        val editor = sharedPrefs.edit()
        
        editor.putBoolean("enabled", binding.switchNotificationEnabled.isChecked)
        editor.putString("interval", binding.editTextNotificationInterval.text.toString())
        editor.putString("time", binding.editTextNotificationTime.text.toString())
        editor.apply()
        
        updateNotificationSettings()
        Snackbar.make(binding.root, "设置已保存", Snackbar.LENGTH_SHORT).show()
    }
    
    private fun resetSettings() {
        binding.apply {
            switchNotificationEnabled.isChecked = true
            editTextNotificationInterval.setText("24")
            editTextNotificationTime.setText("09:00")
        }
        
        saveSettings()
        Snackbar.make(binding.root, "设置已重置", Snackbar.LENGTH_SHORT).show()
    }
    
    private fun getNextNotificationTime() {
        lifecycleScope.launch {
            val nextTime = notificationManager.getNextNotificationTime()
            if (nextTime != null) {
                val dateFormat = java.text.SimpleDateFormat("yyyy-MM-dd HH:mm", java.util.Locale.getDefault())
                binding.textViewNextNotification.text = "下次通知: ${dateFormat.format(java.util.Date(nextTime))}"
            } else {
                binding.textViewNextNotification.text = "无计划通知"
            }
        }
    }
    
    override fun onResume() {
        super.onResume()
        getNextNotificationTime()
    }
    
    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
