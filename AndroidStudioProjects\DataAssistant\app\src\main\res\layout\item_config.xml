<?xml version="1.0" encoding="utf-8"?>
<com.google.android.material.card.MaterialCardView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/card_view"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:layout_margin="8dp"
    app:cardCornerRadius="12dp"
    app:cardElevation="4dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 标题行 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="8dp">

            <TextView
                android:id="@+id/text_view_config_name"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="配置名称"
                android:textSize="18sp"
                android:textStyle="bold"
                tools:text="乳制品配置" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_default"
                style="@style/Widget.Material3.Chip.Assist"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="默认"
                android:visibility="gone"
                tools:visibility="visible" />

            <Switch
                android:id="@+id/switch_enabled"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_marginStart="8dp" />

        </LinearLayout>

        <!-- 描述 -->
        <TextView
            android:id="@+id/text_view_config_description"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="配置描述"
            android:textSize="14sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginBottom="12dp"
            tools:text="适用于牛奶、酸奶等乳制品的保质期管理" />

        <!-- 配置信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="保质期范围"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

                <TextView
                    android:id="@+id/text_view_shelf_life_range"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="1-30天"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="1-30天" />

            </LinearLayout>

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="通知设置"
                    android:textSize="12sp"
                    android:textColor="@android:color/darker_gray" />

                <TextView
                    android:id="@+id/text_view_notification_info"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="每24小时 09:00"
                    android:textSize="14sp"
                    android:textStyle="bold"
                    tools:text="每24小时 09:00" />

            </LinearLayout>

        </LinearLayout>

        <!-- 提醒天数 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/text_view_warning_days"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="临期: 3天"
                android:textSize="12sp"
                android:textColor="@android:color/holo_orange_dark"
                tools:text="临期: 3天" />

            <TextView
                android:id="@+id/text_view_removal_days"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="下架: 1天"
                android:textSize="12sp"
                android:textColor="@android:color/holo_red_dark"
                tools:text="下架: 1天" />

            <TextView
                android:id="@+id/text_view_discount_days"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:text="折扣: 7天"
                android:textSize="12sp"
                android:textColor="@android:color/holo_blue_dark"
                tools:text="折扣: 7天" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="end">

            <Button
                android:id="@+id/button_set_default"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="设为默认"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_edit"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="编辑"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_delete"
                style="@style/Widget.Material3.Button.TextButton"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="删除"
                android:textColor="@android:color/holo_red_dark"
                android:textSize="12sp" />

        </LinearLayout>

    </LinearLayout>

</com.google.android.material.card.MaterialCardView>
