<?xml version="1.0" encoding="utf-8"?>
<LinearLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:orientation="vertical"
    android:gravity="center"
    android:padding="32dp">

    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="48dp"
        android:layout_height="48dp"
        android:layout_marginBottom="16dp"
        android:visibility="visible"
        tools:visibility="visible" />

    <TextView
        android:id="@+id/text_view_message"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="加载中..."
        android:textSize="16sp"
        android:textColor="@android:color/darker_gray"
        android:layout_marginBottom="16dp"
        android:gravity="center"
        tools:text="加载中..." />

    <Button
        android:id="@+id/button_retry"
        style="@style/Widget.Material3.Button.OutlinedButton"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="重试"
        android:visibility="gone"
        tools:visibility="visible" />

</LinearLayout>
