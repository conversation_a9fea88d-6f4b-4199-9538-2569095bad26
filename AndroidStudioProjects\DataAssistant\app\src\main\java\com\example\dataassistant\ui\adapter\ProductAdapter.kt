package com.example.dataassistant.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.databinding.ItemProductBinding
import com.example.dataassistant.utils.DateUtils
import com.example.dataassistant.utils.ProductStatusCalculator
import android.graphics.Color

/**
 * 商品列表适配器
 */
class ProductAdapter(
    private val onItemClick: (Product) -> Unit,
    private val onItemLongClick: ((Product) -> Boolean)? = null,
    private val onStatusClick: ((Product) -> Unit)? = null
) : ListAdapter<Product, ProductAdapter.ProductViewHolder>(ProductDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ProductViewHolder {
        val binding = ItemProductBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ProductViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ProductViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ProductViewHolder(
        private val binding: ItemProductBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(product: Product) {
            binding.apply {
                // 基本信息
                textViewProductName.text = product.name
                textViewCategory.text = product.category ?: "未分类"
                textViewQuantity.text = "数量: ${product.quantity}"

                // 品牌信息
                if (!product.brand.isNullOrBlank()) {
                    textViewBrand.text = product.brand
                    textViewBrand.visibility = android.view.View.VISIBLE
                } else {
                    textViewBrand.visibility = android.view.View.GONE
                }

                // 日期信息
                textViewProductionDate.text = DateUtils.formatDate(product.productionDate)
                textViewExpiryDate.text = DateUtils.formatDate(product.expiryDate)

                // 计算状态信息
                val statusInfo = ProductStatusCalculator.calculateProductStatus(product)
                textViewStatus.text = statusInfo.statusText
                textViewRemainingDays.text = DateUtils.formatRemainingTime(statusInfo.remainingDays)

                // 设置状态颜色
                val statusColor = Color.parseColor(statusInfo.statusColor)
                textViewStatus.setTextColor(statusColor)
                viewStatusIndicator.setBackgroundColor(statusColor)

                // 点击事件
                root.setOnClickListener { onItemClick(product) }
                onItemLongClick?.let { callback ->
                    root.setOnLongClickListener { callback(product) }
                }
                onStatusClick?.let { callback ->
                    textViewStatus.setOnClickListener { callback(product) }
                }
            }
        }
    }

    private class ProductDiffCallback : DiffUtil.ItemCallback<Product>() {
        override fun areItemsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: Product, newItem: Product): Boolean {
            return oldItem == newItem
        }
    }
}
