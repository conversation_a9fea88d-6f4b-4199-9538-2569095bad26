package com.example.dataassistant.utils

import org.junit.Assert.*
import org.junit.Test
import java.text.SimpleDateFormat
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * DateUtils单元测试
 */
class DateUtilsTest {

    @Test
    fun testFormatDate() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val timestamp = calendar.timeInMillis
        
        val formatted = DateUtils.formatDate(timestamp)
        assertEquals("2024-01-15", formatted)
    }

    @Test
    fun testFormatDateTime() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 14, 30, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val timestamp = calendar.timeInMillis
        
        val formatted = DateUtils.formatDateTime(timestamp)
        assertEquals("2024-01-15 14:30", formatted)
    }

    @Test
    fun testFormatTime() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 14, 30, 0)
        val timestamp = calendar.timeInMillis
        
        val formatted = DateUtils.formatTime(timestamp)
        assertEquals("14:30", formatted)
    }

    @Test
    fun testParseDate() {
        // 测试有效日期
        val validDate = "2024-01-15"
        val parsed = DateUtils.parseDate(validDate)
        assertNotNull(parsed)
        
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = parsed!!
        assertEquals(2024, calendar.get(Calendar.YEAR))
        assertEquals(Calendar.JANUARY, calendar.get(Calendar.MONTH))
        assertEquals(15, calendar.get(Calendar.DAY_OF_MONTH))

        // 测试无效日期
        val invalidDate = "invalid-date"
        val parsedInvalid = DateUtils.parseDate(invalidDate)
        assertNull(parsedInvalid)

        // 测试空字符串
        val emptyDate = ""
        val parsedEmpty = DateUtils.parseDate(emptyDate)
        assertNull(parsedEmpty)
    }

    @Test
    fun testParseDateTime() {
        // 测试有效日期时间
        val validDateTime = "2024-01-15 14:30"
        val parsed = DateUtils.parseDateTime(validDateTime)
        assertNotNull(parsed)
        
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = parsed!!
        assertEquals(2024, calendar.get(Calendar.YEAR))
        assertEquals(Calendar.JANUARY, calendar.get(Calendar.MONTH))
        assertEquals(15, calendar.get(Calendar.DAY_OF_MONTH))
        assertEquals(14, calendar.get(Calendar.HOUR_OF_DAY))
        assertEquals(30, calendar.get(Calendar.MINUTE))

        // 测试无效日期时间
        val invalidDateTime = "invalid-datetime"
        val parsedInvalid = DateUtils.parseDateTime(invalidDateTime)
        assertNull(parsedInvalid)
    }

    @Test
    fun testGetToday() {
        val today = DateUtils.getToday()
        val now = System.currentTimeMillis()
        
        // 检查是否是今天的开始时间（00:00:00）
        val calendar = Calendar.getInstance()
        calendar.timeInMillis = today
        assertEquals(0, calendar.get(Calendar.HOUR_OF_DAY))
        assertEquals(0, calendar.get(Calendar.MINUTE))
        assertEquals(0, calendar.get(Calendar.SECOND))
        assertEquals(0, calendar.get(Calendar.MILLISECOND))
        
        // 检查日期是否正确
        val nowCalendar = Calendar.getInstance()
        nowCalendar.timeInMillis = now
        assertEquals(nowCalendar.get(Calendar.YEAR), calendar.get(Calendar.YEAR))
        assertEquals(nowCalendar.get(Calendar.MONTH), calendar.get(Calendar.MONTH))
        assertEquals(nowCalendar.get(Calendar.DAY_OF_MONTH), calendar.get(Calendar.DAY_OF_MONTH))
    }

    @Test
    fun testGetTomorrow() {
        val tomorrow = DateUtils.getTomorrow()
        val today = DateUtils.getToday()
        
        // 明天应该比今天多一天
        assertEquals(TimeUnit.DAYS.toMillis(1), tomorrow - today)
    }

    @Test
    fun testGetYesterday() {
        val yesterday = DateUtils.getYesterday()
        val today = DateUtils.getToday()
        
        // 昨天应该比今天少一天
        assertEquals(TimeUnit.DAYS.toMillis(1), today - yesterday)
    }

    @Test
    fun testIsToday() {
        val now = System.currentTimeMillis()
        assertTrue(DateUtils.isToday(now))
        
        val yesterday = now - TimeUnit.DAYS.toMillis(1)
        assertFalse(DateUtils.isToday(yesterday))
        
        val tomorrow = now + TimeUnit.DAYS.toMillis(1)
        assertFalse(DateUtils.isToday(tomorrow))
    }

    @Test
    fun testIsSameDay() {
        val calendar1 = Calendar.getInstance()
        calendar1.set(2024, Calendar.JANUARY, 15, 10, 30, 0)
        val time1 = calendar1.timeInMillis
        
        val calendar2 = Calendar.getInstance()
        calendar2.set(2024, Calendar.JANUARY, 15, 20, 45, 0)
        val time2 = calendar2.timeInMillis
        
        val calendar3 = Calendar.getInstance()
        calendar3.set(2024, Calendar.JANUARY, 16, 10, 30, 0)
        val time3 = calendar3.timeInMillis
        
        assertTrue(DateUtils.isSameDay(time1, time2))
        assertFalse(DateUtils.isSameDay(time1, time3))
    }

    @Test
    fun testGetDaysBetween() {
        val calendar1 = Calendar.getInstance()
        calendar1.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        calendar1.set(Calendar.MILLISECOND, 0)
        val date1 = calendar1.timeInMillis
        
        val calendar2 = Calendar.getInstance()
        calendar2.set(2024, Calendar.JANUARY, 20, 0, 0, 0)
        calendar2.set(Calendar.MILLISECOND, 0)
        val date2 = calendar2.timeInMillis
        
        assertEquals(5, DateUtils.getDaysBetween(date1, date2))
        assertEquals(-5, DateUtils.getDaysBetween(date2, date1))
        assertEquals(0, DateUtils.getDaysBetween(date1, date1))
    }

    @Test
    fun testAddDays() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        calendar.set(Calendar.MILLISECOND, 0)
        val originalDate = calendar.timeInMillis
        
        val futureDate = DateUtils.addDays(originalDate, 5)
        val pastDate = DateUtils.addDays(originalDate, -3)
        
        assertEquals(5, DateUtils.getDaysBetween(originalDate, futureDate))
        assertEquals(-3, DateUtils.getDaysBetween(originalDate, pastDate))
    }

    @Test
    fun testFormatRemainingTime() {
        // 测试正数天数
        assertEquals("还有5天", DateUtils.formatRemainingTime(5))
        assertEquals("还有1天", DateUtils.formatRemainingTime(1))
        assertEquals("今天到期", DateUtils.formatRemainingTime(0))
        
        // 测试负数天数（已过期）
        assertEquals("已过期1天", DateUtils.formatRemainingTime(-1))
        assertEquals("已过期5天", DateUtils.formatRemainingTime(-5))
    }

    @Test
    fun testGetWeekOfYear() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        val timestamp = calendar.timeInMillis
        
        val week = DateUtils.getWeekOfYear(timestamp)
        assertTrue(week >= 1 && week <= 53)
    }

    @Test
    fun testGetMonthName() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        val timestamp = calendar.timeInMillis
        
        val monthName = DateUtils.getMonthName(timestamp)
        assertEquals("1月", monthName)
        
        calendar.set(Calendar.DECEMBER, 15)
        val decemberTimestamp = calendar.timeInMillis
        val decemberName = DateUtils.getMonthName(decemberTimestamp)
        assertEquals("12月", decemberName)
    }

    @Test
    fun testGetDayOfWeek() {
        // 创建一个已知的星期一（2024年1月15日是星期一）
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        val mondayTimestamp = calendar.timeInMillis
        
        val dayOfWeek = DateUtils.getDayOfWeek(mondayTimestamp)
        assertEquals("星期一", dayOfWeek)
    }

    @Test
    fun testIsWeekend() {
        // 创建一个星期六（2024年1月13日）
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 13, 0, 0, 0)
        val saturdayTimestamp = calendar.timeInMillis
        assertTrue(DateUtils.isWeekend(saturdayTimestamp))
        
        // 创建一个星期一（2024年1月15日）
        calendar.set(2024, Calendar.JANUARY, 15, 0, 0, 0)
        val mondayTimestamp = calendar.timeInMillis
        assertFalse(DateUtils.isWeekend(mondayTimestamp))
    }

    @Test
    fun testGetStartOfDay() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 14, 30, 45)
        calendar.set(Calendar.MILLISECOND, 123)
        val timestamp = calendar.timeInMillis
        
        val startOfDay = DateUtils.getStartOfDay(timestamp)
        
        val resultCalendar = Calendar.getInstance()
        resultCalendar.timeInMillis = startOfDay
        assertEquals(2024, resultCalendar.get(Calendar.YEAR))
        assertEquals(Calendar.JANUARY, resultCalendar.get(Calendar.MONTH))
        assertEquals(15, resultCalendar.get(Calendar.DAY_OF_MONTH))
        assertEquals(0, resultCalendar.get(Calendar.HOUR_OF_DAY))
        assertEquals(0, resultCalendar.get(Calendar.MINUTE))
        assertEquals(0, resultCalendar.get(Calendar.SECOND))
        assertEquals(0, resultCalendar.get(Calendar.MILLISECOND))
    }

    @Test
    fun testGetEndOfDay() {
        val calendar = Calendar.getInstance()
        calendar.set(2024, Calendar.JANUARY, 15, 14, 30, 45)
        val timestamp = calendar.timeInMillis
        
        val endOfDay = DateUtils.getEndOfDay(timestamp)
        
        val resultCalendar = Calendar.getInstance()
        resultCalendar.timeInMillis = endOfDay
        assertEquals(2024, resultCalendar.get(Calendar.YEAR))
        assertEquals(Calendar.JANUARY, resultCalendar.get(Calendar.MONTH))
        assertEquals(15, resultCalendar.get(Calendar.DAY_OF_MONTH))
        assertEquals(23, resultCalendar.get(Calendar.HOUR_OF_DAY))
        assertEquals(59, resultCalendar.get(Calendar.MINUTE))
        assertEquals(59, resultCalendar.get(Calendar.SECOND))
        assertEquals(999, resultCalendar.get(Calendar.MILLISECOND))
    }
}
