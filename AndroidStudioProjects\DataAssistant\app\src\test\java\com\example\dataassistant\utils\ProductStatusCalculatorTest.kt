package com.example.dataassistant.utils

import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.entity.ShelfLifeConfig
import org.junit.Assert.*
import org.junit.Test
import java.util.*
import java.util.concurrent.TimeUnit

/**
 * ProductStatusCalculator单元测试
 */
class ProductStatusCalculatorTest {

    @Test
    fun testCalculateShelfLifeDays() {
        // 测试天数
        assertEquals(7, ProductStatusCalculator.calculateShelfLifeDays(7, "天"))
        assertEquals(7, ProductStatusCalculator.calculateShelfLifeDays(7, "day"))

        // 测试周数
        assertEquals(14, ProductStatusCalculator.calculateShelfLifeDays(2, "周"))
        assertEquals(14, ProductStatusCalculator.calculateShelfLifeDays(2, "week"))

        // 测试月数
        assertEquals(60, ProductStatusCalculator.calculateShelfLifeDays(2, "月"))
        assertEquals(60, ProductStatusCalculator.calculateShelfLifeDays(2, "month"))

        // 测试年数
        assertEquals(730, ProductStatusCalculator.calculateShelfLifeDays(2, "年"))
        assertEquals(730, ProductStatusCalculator.calculateShelfLifeDays(2, "year"))

        // 测试未知单位（默认为天）
        assertEquals(5, ProductStatusCalculator.calculateShelfLifeDays(5, "unknown"))
    }

    @Test
    fun testCalculateExpiryDate() {
        val productionDate = System.currentTimeMillis()
        val shelfLifeDays = 30
        val expectedExpiryDate = productionDate + TimeUnit.DAYS.toMillis(30)
        
        val actualExpiryDate = ProductStatusCalculator.calculateExpiryDate(productionDate, shelfLifeDays)
        assertEquals(expectedExpiryDate, actualExpiryDate)
    }

    @Test
    fun testCalculateProductStatus() {
        val now = System.currentTimeMillis()
        val productionDate = now - TimeUnit.DAYS.toMillis(10)
        
        // 测试正常状态商品（还有20天过期）
        val normalProduct = createTestProduct(
            productionDate = productionDate,
            expiryDate = now + TimeUnit.DAYS.toMillis(20),
            warningDays = 7,
            removalDays = 3
        )
        
        val normalStatus = ProductStatusCalculator.calculateProductStatus(normalProduct)
        assertEquals(ProductStatus.NORMAL, normalStatus.status)
        assertEquals("正常", normalStatus.statusText)
        assertEquals(20, normalStatus.remainingDays)
        assertTrue(normalStatus.statusColor.isNotEmpty())

        // 测试临期商品（还有5天过期）
        val warningProduct = createTestProduct(
            productionDate = productionDate,
            expiryDate = now + TimeUnit.DAYS.toMillis(5),
            warningDays = 7,
            removalDays = 3
        )
        
        val warningStatus = ProductStatusCalculator.calculateProductStatus(warningProduct)
        assertEquals(ProductStatus.WARNING, warningStatus.status)
        assertEquals("临期", warningStatus.statusText)
        assertEquals(5, warningStatus.remainingDays)

        // 测试需下架商品（还有2天过期）
        val removalProduct = createTestProduct(
            productionDate = productionDate,
            expiryDate = now + TimeUnit.DAYS.toMillis(2),
            warningDays = 7,
            removalDays = 3
        )
        
        val removalStatus = ProductStatusCalculator.calculateProductStatus(removalProduct)
        assertEquals(ProductStatus.REMOVAL, removalStatus.status)
        assertEquals("需下架", removalStatus.statusText)
        assertEquals(2, removalStatus.remainingDays)

        // 测试过期商品（过期2天）
        val expiredProduct = createTestProduct(
            productionDate = productionDate,
            expiryDate = now - TimeUnit.DAYS.toMillis(2),
            warningDays = 7,
            removalDays = 3
        )
        
        val expiredStatus = ProductStatusCalculator.calculateProductStatus(expiredProduct)
        assertEquals(ProductStatus.EXPIRED, expiredStatus.status)
        assertEquals("已过期", expiredStatus.statusText)
        assertEquals(-2, expiredStatus.remainingDays)
    }

    @Test
    fun testCalculateProductStatusWithConfig() {
        val now = System.currentTimeMillis()
        val productionDate = now - TimeUnit.DAYS.toMillis(10)
        
        val product = createTestProduct(
            productionDate = productionDate,
            expiryDate = now + TimeUnit.DAYS.toMillis(5),
            warningDays = 3,  // 商品设置的提醒天数
            removalDays = 1
        )
        
        val config = ShelfLifeConfig(
            id = UUID.randomUUID().toString(),
            name = "测试配置",
            description = "测试",
            minShelfLifeDays = 1,
            maxShelfLifeDays = 365,
            warningDays = 7,  // 配置的提醒天数（应该覆盖商品设置）
            removalDays = 3,
            discountDays = 10,
            notificationFrequency = 24,
            notificationTime = "09:00",
            isEnabled = true,
            isNotificationEnabled = true,
            isDefault = false,
            createTime = System.currentTimeMillis(),
            updateTime = System.currentTimeMillis()
        )
        
        val status = ProductStatusCalculator.calculateProductStatus(product, config)
        // 由于配置的warningDays是7，而商品还有5天过期，所以应该是临期状态
        assertEquals(ProductStatus.WARNING, status.status)
    }

    @Test
    fun testShouldNotify() {
        val now = System.currentTimeMillis()
        
        // 测试启用通知的临期商品
        val warningProduct = createTestProduct(
            expiryDate = now + TimeUnit.DAYS.toMillis(2),
            warningDays = 3,
            isNotificationEnabled = true
        )
        assertTrue(ProductStatusCalculator.shouldNotify(warningProduct))

        // 测试禁用通知的临期商品
        val warningProductNoNotification = warningProduct.copy(isNotificationEnabled = false)
        assertFalse(ProductStatusCalculator.shouldNotify(warningProductNoNotification))

        // 测试正常状态商品
        val normalProduct = createTestProduct(
            expiryDate = now + TimeUnit.DAYS.toMillis(10),
            warningDays = 3,
            isNotificationEnabled = true
        )
        assertFalse(ProductStatusCalculator.shouldNotify(normalProduct))

        // 测试过期商品
        val expiredProduct = createTestProduct(
            expiryDate = now - TimeUnit.DAYS.toMillis(1),
            isNotificationEnabled = true
        )
        assertTrue(ProductStatusCalculator.shouldNotify(expiredProduct))
    }

    @Test
    fun testGetNotificationMessage() {
        val now = System.currentTimeMillis()
        
        // 测试临期商品通知消息
        val warningProduct = createTestProduct(
            name = "测试商品",
            expiryDate = now + TimeUnit.DAYS.toMillis(2),
            warningDays = 3
        )
        val warningMessage = ProductStatusCalculator.getNotificationMessage(warningProduct)
        assertNotNull(warningMessage)
        assertTrue(warningMessage!!.contains("测试商品"))
        assertTrue(warningMessage.contains("2"))

        // 测试过期商品通知消息
        val expiredProduct = createTestProduct(
            name = "过期商品",
            expiryDate = now - TimeUnit.DAYS.toMillis(1)
        )
        val expiredMessage = ProductStatusCalculator.getNotificationMessage(expiredProduct)
        assertNotNull(expiredMessage)
        assertTrue(expiredMessage!!.contains("过期商品"))
        assertTrue(expiredMessage.contains("已过期"))

        // 测试正常商品（不应该有通知消息）
        val normalProduct = createTestProduct(
            expiryDate = now + TimeUnit.DAYS.toMillis(10),
            warningDays = 3
        )
        val normalMessage = ProductStatusCalculator.getNotificationMessage(normalProduct)
        assertNull(normalMessage)
    }

    @Test
    fun testGetStatusText() {
        assertEquals("正常", ProductStatusCalculator.getStatusText(ProductStatus.NORMAL, 10))
        assertEquals("临期", ProductStatusCalculator.getStatusText(ProductStatus.WARNING, 2))
        assertEquals("已过期", ProductStatusCalculator.getStatusText(ProductStatus.EXPIRED, -1))
        assertEquals("需下架 (1天)", ProductStatusCalculator.getStatusText(ProductStatus.REMOVAL, 1))
    }

    private fun createTestProduct(
        id: String = UUID.randomUUID().toString(),
        name: String = "测试商品",
        productionDate: Long = System.currentTimeMillis() - TimeUnit.DAYS.toMillis(10),
        expiryDate: Long = System.currentTimeMillis() + TimeUnit.DAYS.toMillis(30),
        stockDate: Long = System.currentTimeMillis(),
        quantity: Int = 1,
        shelfLifeDays: Int = 30,
        shelfLifeNumber: Int = 30,
        shelfLifeUnit: String = "天",
        warningDays: Int = 3,
        removalDays: Int = 1,
        status: ProductStatus = ProductStatus.NORMAL,
        isNotificationEnabled: Boolean = true
    ): Product {
        return Product(
            id = id,
            name = name,
            productionDate = productionDate,
            expiryDate = expiryDate,
            stockDate = stockDate,
            quantity = quantity,
            shelfLifeDays = shelfLifeDays,
            shelfLifeNumber = shelfLifeNumber,
            shelfLifeUnit = shelfLifeUnit,
            warningDays = warningDays,
            removalDays = removalDays,
            status = status,
            isNotificationEnabled = isNotificationEnabled,
            createTime = System.currentTimeMillis(),
            updateTime = System.currentTimeMillis()
        )
    }
}
