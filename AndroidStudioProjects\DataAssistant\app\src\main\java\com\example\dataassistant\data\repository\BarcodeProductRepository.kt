package com.example.dataassistant.data.repository

import com.example.dataassistant.data.dao.BarcodeProductDao
import com.example.dataassistant.data.entity.BarcodeProduct
import com.example.dataassistant.data.entity.BarcodeQueryResult
import com.example.dataassistant.data.entity.ProductSuggestion
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 条码商品数据仓库
 */
@Singleton
class BarcodeProductRepository @Inject constructor(
    private val barcodeProductDao: BarcodeProductDao
) {

    /**
     * 根据条码查询商品
     */
    suspend fun getProductByBarcode(barcode: String): BarcodeQueryResult {
        return try {
            val startTime = System.currentTimeMillis()
            val product = barcodeProductDao.getProductByBarcode(barcode)
            val queryTime = System.currentTimeMillis() - startTime

            if (product != null) {
                // 更新最后使用时间
                barcodeProductDao.updateLastUsedTime(barcode, System.currentTimeMillis())
                BarcodeQueryResult(
                    success = true,
                    product = product,
                    source = product.source,
                    queryTime = queryTime
                )
            } else {
                BarcodeQueryResult(
                    success = false,
                    error = "未找到商品信息",
                    queryTime = queryTime
                )
            }
        } catch (e: Exception) {
            BarcodeQueryResult(
                success = false,
                error = e.message ?: "查询失败"
            )
        }
    }

    /**
     * 搜索商品
     */
    suspend fun searchProducts(keyword: String, limit: Int = 20): List<BarcodeProduct> =
        barcodeProductDao.searchProducts(keyword, limit)

    /**
     * 获取搜索建议
     */
    suspend fun getSearchSuggestions(keyword: String, limit: Int = 10): List<ProductSuggestion> {
        val products = barcodeProductDao.getSearchSuggestions(keyword, limit)
        return products.map { product ->
            ProductSuggestion(
                barcode = product.barcode,
                name = product.name,
                brand = product.brand,
                category = product.category,
                fullName = product.fullName,
                source = product.source
            )
        }
    }

    /**
     * 插入商品
     */
    suspend fun insertProduct(product: BarcodeProduct) = barcodeProductDao.insertProduct(product)

    /**
     * 批量插入商品
     */
    suspend fun insertProducts(products: List<BarcodeProduct>) =
        barcodeProductDao.insertProducts(products)

    /**
     * 更新商品
     */
    suspend fun updateProduct(product: BarcodeProduct) = barcodeProductDao.updateProduct(product)

    /**
     * 删除商品
     */
    suspend fun deleteProduct(product: BarcodeProduct) = barcodeProductDao.deleteProduct(product)

    /**
     * 根据条码删除商品
     */
    suspend fun deleteProductByBarcode(barcode: String) =
        barcodeProductDao.deleteProductByBarcode(barcode)

    /**
     * 获取所有商品
     */
    suspend fun getAllProducts(): List<BarcodeProduct> = barcodeProductDao.getAllProducts()

    /**
     * 获取按来源分组的商品数量
     */
    suspend fun getProductCountBySource(): Map<String, Int> =
        barcodeProductDao.getProductCountBySource().associate { it.source to it.count }

    /**
     * 获取最近使用的商品
     */
    suspend fun getRecentlyUsedProducts(limit: Int = 10): List<BarcodeProduct> =
        barcodeProductDao.getRecentlyUsedProducts(limit)

    /**
     * 获取高可信度商品
     */
    suspend fun getHighConfidenceProducts(minConfidence: Float = 0.8f): List<BarcodeProduct> =
        barcodeProductDao.getHighConfidenceProducts(minConfidence)

    /**
     * 清空所有商品
     */
    suspend fun deleteAllProducts() = barcodeProductDao.deleteAllProducts()
}
