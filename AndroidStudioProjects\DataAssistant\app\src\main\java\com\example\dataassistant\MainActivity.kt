package com.example.dataassistant

import android.content.Intent
import android.os.Bundle
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.findNavController
import androidx.navigation.ui.AppBarConfiguration
import androidx.navigation.ui.navigateUp
import androidx.navigation.ui.setupActionBarWithNavController
import androidx.navigation.ui.setupWithNavController
import android.view.Menu
import android.view.MenuItem
import com.example.dataassistant.databinding.ActivityMainBinding

class MainActivity : AppCompatActivity() {

    private lateinit var appBarConfiguration: AppBarConfiguration
    private lateinit var binding: ActivityMainBinding

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)

        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        setSupportActionBar(binding.toolbar)

        val navController = findNavController(R.id.nav_host_fragment_content_main)

        // 设置顶级目的地（不显示返回按钮的页面）
        appBarConfiguration = AppBarConfiguration(
            setOf(
                R.id.navigation_home,
                R.id.navigation_add,
                R.id.navigation_list,
                R.id.navigation_config
            )
        )

        setupActionBarWithNavController(navController, appBarConfiguration)

        // 设置底部导航
        binding.bottomNavigation.setupWithNavController(navController)

        // 处理通知点击
        handleNotificationIntent(intent)
    }

    override fun onNewIntent(intent: Intent?) {
        super.onNewIntent(intent)
        intent?.let { handleNotificationIntent(it) }
    }

    private fun handleNotificationIntent(intent: Intent) {
        val productId = intent.getStringExtra("product_id")
        val openProductDetail = intent.getBooleanExtra("open_product_detail", false)
        val openProductList = intent.getBooleanExtra("open_product_list", false)

        val navController = findNavController(R.id.nav_host_fragment_content_main)

        when {
            openProductDetail && !productId.isNullOrEmpty() -> {
                // 导航到商品详情
                try {
                    navController.navigate(R.id.product_detail)
                } catch (e: Exception) {
                    // 如果导航失败，导航到商品列表
                    navController.navigate(R.id.navigation_list)
                }
            }
            openProductList -> {
                // 导航到商品列表
                navController.navigate(R.id.navigation_list)
            }
        }
    }

    override fun onCreateOptionsMenu(menu: Menu): Boolean {
        // Inflate the menu; this adds items to the action bar if it is present.
        menuInflater.inflate(R.menu.menu_main, menu)
        return true
    }

    override fun onOptionsItemSelected(item: MenuItem): Boolean {
        // Handle action bar item clicks here. The action bar will
        // automatically handle clicks on the Home/Up button, so long
        // as you specify a parent activity in AndroidManifest.xml.
        return when (item.itemId) {
            R.id.action_settings -> true
            else -> super.onOptionsItemSelected(item)
        }
    }

    override fun onSupportNavigateUp(): Boolean {
        val navController = findNavController(R.id.nav_host_fragment_content_main)
        return navController.navigateUp(appBarConfiguration)
                || super.onSupportNavigateUp()
    }
}
