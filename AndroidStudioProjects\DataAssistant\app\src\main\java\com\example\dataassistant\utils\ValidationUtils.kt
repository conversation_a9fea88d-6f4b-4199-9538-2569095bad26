package com.example.dataassistant.utils

import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ShelfLifeConfig

/**
 * 数据验证工具类
 */
object ValidationUtils {

    /**
     * 验证商品数据
     */
    fun validateProduct(product: Product): ValidationResult {
        val errors = mutableListOf<String>()

        // 验证商品名称
        if (product.name.isBlank()) {
            errors.add("商品名称不能为空")
        } else if (product.name.length > 100) {
            errors.add("商品名称不能超过100个字符")
        }

        // 验证日期
        if (product.productionDate <= 0) {
            errors.add("生产日期无效")
        }

        if (product.expiryDate <= 0) {
            errors.add("到期日期无效")
        }

        if (product.stockDate <= 0) {
            errors.add("入库日期无效")
        }

        // 验证日期逻辑
        if (product.productionDate > product.expiryDate) {
            errors.add("生产日期不能晚于到期日期")
        }

        if (product.stockDate < product.productionDate) {
            errors.add("入库日期不能早于生产日期")
        }

        // 验证保质期
        if (product.shelfLifeDays <= 0) {
            errors.add("保质期天数必须大于0")
        }

        if (product.shelfLifeNumber <= 0) {
            errors.add("保质期数量必须大于0")
        }

        if (product.shelfLifeUnit.isBlank()) {
            errors.add("保质期单位不能为空")
        }

        // 验证提醒天数
        if (product.warningDays < 0) {
            errors.add("临期提醒天数不能为负数")
        }

        if (product.removalDays < 0) {
            errors.add("下架提醒天数不能为负数")
        }

        if (product.warningDays < product.removalDays) {
            errors.add("临期提醒天数不能小于下架提醒天数")
        }

        // 验证数量
        if (product.quantity <= 0) {
            errors.add("商品数量必须大于0")
        }

        // 验证价格
        if (product.price != null && product.price < 0) {
            errors.add("商品价格不能为负数")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证保质期配置
     */
    fun validateShelfLifeConfig(config: ShelfLifeConfig): ValidationResult {
        val errors = mutableListOf<String>()

        // 验证配置名称
        if (config.name.isBlank()) {
            errors.add("配置名称不能为空")
        } else if (config.name.length > 50) {
            errors.add("配置名称不能超过50个字符")
        }

        // 验证保质期范围
        if (config.minShelfLifeDays <= 0) {
            errors.add("最小保质期天数必须大于0")
        }

        if (config.maxShelfLifeDays <= 0) {
            errors.add("最大保质期天数必须大于0")
        }

        if (config.minShelfLifeDays > config.maxShelfLifeDays) {
            errors.add("最小保质期天数不能大于最大保质期天数")
        }

        // 验证提醒天数
        if (config.warningDays < 0) {
            errors.add("临期提醒天数不能为负数")
        }

        if (config.removalDays < 0) {
            errors.add("下架提醒天数不能为负数")
        }

        if (config.discountDays < 0) {
            errors.add("折扣提醒天数不能为负数")
        }

        if (config.warningDays < config.removalDays) {
            errors.add("临期提醒天数不能小于下架提醒天数")
        }

        // 验证通知频率
        if (config.notificationFrequency <= 0) {
            errors.add("通知频率必须大于0")
        }

        // 验证通知时间格式
        if (!isValidTimeFormat(config.notificationTime)) {
            errors.add("通知时间格式无效，应为HH:mm格式")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证条码格式
     */
    fun validateBarcode(barcode: String): ValidationResult {
        val errors = mutableListOf<String>()

        if (barcode.isBlank()) {
            errors.add("条码不能为空")
        } else {
            // 检查条码长度（常见条码长度为8、12、13、14位）
            if (barcode.length !in listOf(8, 12, 13, 14)) {
                errors.add("条码长度无效，应为8、12、13或14位")
            }

            // 检查是否只包含数字
            if (!barcode.all { it.isDigit() }) {
                errors.add("条码只能包含数字")
            }
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证搜索关键词
     */
    fun validateSearchKeyword(keyword: String): ValidationResult {
        val errors = mutableListOf<String>()

        if (keyword.isBlank()) {
            errors.add("搜索关键词不能为空")
        } else if (keyword.length < 2) {
            errors.add("搜索关键词至少需要2个字符")
        } else if (keyword.length > 50) {
            errors.add("搜索关键词不能超过50个字符")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证日期字符串格式
     */
    fun validateDateString(dateString: String): ValidationResult {
        val errors = mutableListOf<String>()

        if (dateString.isBlank()) {
            errors.add("日期不能为空")
        } else if (DateUtils.parseDate(dateString)?.let { it > 0 } != true) {
            errors.add("日期格式无效，应为yyyy-MM-dd格式")
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证时间格式（HH:mm）
     */
    private fun isValidTimeFormat(time: String): Boolean {
        val timeRegex = Regex("^([01]?[0-9]|2[0-3]):[0-5][0-9]$")
        return timeRegex.matches(time)
    }

    /**
     * 验证邮箱格式
     */
    fun validateEmail(email: String): ValidationResult {
        val errors = mutableListOf<String>()

        if (email.isBlank()) {
            errors.add("邮箱不能为空")
        } else {
            val emailRegex = Regex("^[A-Za-z0-9+_.-]+@[A-Za-z0-9.-]+\\.[A-Za-z]{2,}$")
            if (!emailRegex.matches(email)) {
                errors.add("邮箱格式无效")
            }
        }

        return ValidationResult(errors.isEmpty(), errors)
    }

    /**
     * 验证手机号格式
     */
    fun validatePhoneNumber(phone: String): ValidationResult {
        val errors = mutableListOf<String>()

        if (phone.isBlank()) {
            errors.add("手机号不能为空")
        } else {
            val phoneRegex = Regex("^1[3-9]\\d{9}$")
            if (!phoneRegex.matches(phone)) {
                errors.add("手机号格式无效")
            }
        }

        return ValidationResult(errors.isEmpty(), errors)
    }
}

/**
 * 验证结果
 */
data class ValidationResult(
    val isValid: Boolean,
    val errors: List<String> = emptyList()
) {
    /**
     * 获取错误消息
     */
    fun getErrorMessage(): String {
        return errors.joinToString("\n")
    }

    /**
     * 获取第一个错误消息
     */
    fun getFirstError(): String? {
        return errors.firstOrNull()
    }
}
