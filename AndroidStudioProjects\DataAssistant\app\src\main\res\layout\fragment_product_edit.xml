<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 基本信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="基本信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="商品名称 *">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_product_name"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="品牌">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_brand"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="分类">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_category"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="text" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="条码">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_barcode"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number"
                            android:enabled="false" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="数量">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_quantity"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="存放位置">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_location"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="text" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 日期信息 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="日期信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="生产日期 *">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_production_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="入库日期">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_stock_date"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:focusable="false"
                            android:clickable="true"
                            android:inputType="none" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

                <!-- 保质期设置 -->
                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="保质期设置"
                    android:textSize="16sp"
                    android:textStyle="bold"
                    android:layout_marginTop="16dp"
                    android:layout_marginBottom="8dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="保质期数量 *">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_shelf_life_number"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <Spinner
                        android:id="@+id/spinner_shelf_life_unit"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:layout_marginTop="8dp" />

                </LinearLayout>

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginTop="8dp"
                    android:hint="到期日期（自动计算）">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_expiry_date"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:inputType="none" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 提醒设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="提醒设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginEnd="8dp"
                        android:hint="临期提醒（天）">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_warning_days"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                    <com.google.android.material.textfield.TextInputLayout
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:layout_marginStart="8dp"
                        android:hint="下架提醒（天）">

                        <com.google.android.material.textfield.TextInputEditText
                            android:id="@+id/edit_text_removal_days"
                            android:layout_width="match_parent"
                            android:layout_height="wrap_content"
                            android:inputType="number" />

                    </com.google.android.material.textfield.TextInputLayout>

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 备注 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="备注信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:hint="备注">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_notes"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="textMultiLine"
                        android:lines="3"
                        android:maxLines="5" />

                </com.google.android.material.textfield.TextInputLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/button_cancel"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="取消" />

            <Button
                android:id="@+id/button_save"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="保存更改" />

        </LinearLayout>

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
