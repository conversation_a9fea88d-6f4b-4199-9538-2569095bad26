package com.example.dataassistant.ui.fragment

import android.Manifest
import android.content.pm.PackageManager
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.activity.result.contract.ActivityResultContracts
import androidx.core.content.ContextCompat
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.lifecycleScope
import com.example.dataassistant.R
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ShelfLifeUnit
import com.example.dataassistant.databinding.FragmentAddProductBinding
import com.example.dataassistant.ui.viewmodel.BarcodeViewModel
import com.example.dataassistant.ui.viewmodel.ProductViewModel
import com.example.dataassistant.utils.DateUtils
import com.example.dataassistant.utils.ProductStatusCalculator
import com.google.android.material.datepicker.MaterialDatePicker
import com.google.android.material.snackbar.Snackbar
import com.journeyapps.barcodescanner.ScanContract
import com.journeyapps.barcodescanner.ScanIntentResult
import com.journeyapps.barcodescanner.ScanOptions
import dagger.hilt.android.AndroidEntryPoint
import kotlinx.coroutines.launch
import java.util.*

/**
 * 添加商品Fragment
 */
@AndroidEntryPoint
class AddProductFragment : Fragment() {

    private var _binding: FragmentAddProductBinding? = null
    private val binding get() = _binding!!

    private val productViewModel: ProductViewModel by viewModels()
    private val barcodeViewModel: BarcodeViewModel by viewModels()

    // 条码扫描器
    private val barcodeLauncher = registerForActivityResult(ScanContract()) { result: ScanIntentResult ->
        if (result.contents != null) {
            handleBarcodeResult(result.contents)
        }
    }

    // 权限请求
    private val requestPermissionLauncher = registerForActivityResult(
        ActivityResultContracts.RequestPermission()
    ) { isGranted: Boolean ->
        if (isGranted) {
            startBarcodeScanner()
        } else {
            Snackbar.make(binding.root, "需要相机权限才能扫描条码", Snackbar.LENGTH_LONG).show()
        }
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentAddProductBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupUI()
        setupObservers()
        setupClickListeners()

        // 设置默认值
        setDefaultValues()
    }

    private fun setupUI() {
        // 设置保质期单位下拉框
        val shelfLifeUnits = ShelfLifeUnit.values().map { it.displayName }
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, shelfLifeUnits)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerShelfLifeUnit.adapter = adapter
    }

    private fun setupObservers() {
        // 观察商品ViewModel状态
        productViewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            binding.progressBar.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE

            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                productViewModel.clearError()
            }

            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                productViewModel.clearMessage()
                // 成功添加后清空表单
                clearForm()
            }
        }

        // 观察条码扫描结果
        barcodeViewModel.queryResult.observe(viewLifecycleOwner) { result ->
            result?.let {
                if (it.success && it.product != null) {
                    fillProductInfo(it.product)
                } else {
                    Snackbar.make(binding.root, "未找到商品信息，请手动输入", Snackbar.LENGTH_SHORT).show()
                }
            }
        }

        // 观察条码ViewModel状态
        barcodeViewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                barcodeViewModel.clearError()
            }
        }
    }

    private fun setupClickListeners() {
        // 扫码按钮
        binding.buttonScanBarcode.setOnClickListener {
            checkCameraPermissionAndScan()
        }

        // 生产日期选择
        binding.editTextProductionDate.setOnClickListener {
            showDatePicker { date ->
                binding.editTextProductionDate.setText(DateUtils.formatDate(date))
                calculateExpiryDate()
            }
        }

        // 入库日期选择
        binding.editTextStockDate.setOnClickListener {
            showDatePicker { date ->
                binding.editTextStockDate.setText(DateUtils.formatDate(date))
            }
        }

        // 保质期数量变化监听
        binding.editTextShelfLifeNumber.setOnFocusChangeListener { _, _ ->
            calculateExpiryDate()
        }

        // 保质期单位变化监听
        binding.spinnerShelfLifeUnit.setOnItemSelectedListener(object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                calculateExpiryDate()
            }
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        })

        // 保存按钮
        binding.buttonSave.setOnClickListener {
            saveProduct()
        }

        // 清空按钮
        binding.buttonClear.setOnClickListener {
            clearForm()
        }
    }

    private fun setDefaultValues() {
        // 设置默认入库日期为今天
        binding.editTextStockDate.setText(DateUtils.formatDate(System.currentTimeMillis()))

        // 设置默认保质期单位为天
        binding.spinnerShelfLifeUnit.setSelection(0)

        // 设置默认数量为1
        binding.editTextQuantity.setText("1")

        // 设置默认提醒天数
        binding.editTextWarningDays.setText("3")
        binding.editTextRemovalDays.setText("1")
    }

    private fun checkCameraPermissionAndScan() {
        when {
            ContextCompat.checkSelfPermission(
                requireContext(),
                Manifest.permission.CAMERA
            ) == PackageManager.PERMISSION_GRANTED -> {
                startBarcodeScanner()
            }
            else -> {
                requestPermissionLauncher.launch(Manifest.permission.CAMERA)
            }
        }
    }

    private fun startBarcodeScanner() {
        val options = ScanOptions()
        options.setDesiredBarcodeFormats(ScanOptions.ALL_CODE_TYPES)
        options.setPrompt("扫描商品条码")
        options.setCameraId(0)
        options.setBeepEnabled(true)
        options.setBarcodeImageEnabled(true)
        options.setOrientationLocked(false)

        barcodeLauncher.launch(options)
    }

    private fun handleBarcodeResult(barcode: String) {
        binding.editTextBarcode.setText(barcode)
        // 查询条码对应的商品信息
        barcodeViewModel.scanBarcode(barcode)
    }

    private fun fillProductInfo(barcodeProduct: com.example.dataassistant.data.entity.BarcodeProduct) {
        binding.apply {
            editTextProductName.setText(barcodeProduct.name)
            editTextBrand.setText(barcodeProduct.brand ?: "")
            editTextCategory.setText(barcodeProduct.category ?: "")
            editTextBarcode.setText(barcodeProduct.barcode)
        }
    }

    private fun showDatePicker(onDateSelected: (Long) -> Unit) {
        val datePicker = MaterialDatePicker.Builder.datePicker()
            .setTitleText("选择日期")
            .setSelection(MaterialDatePicker.todayInUtcMilliseconds())
            .build()

        datePicker.addOnPositiveButtonClickListener { selection ->
            onDateSelected(selection)
        }

        datePicker.show(parentFragmentManager, "DATE_PICKER")
    }

    private fun calculateExpiryDate() {
        val productionDateStr = binding.editTextProductionDate.text.toString()
        val shelfLifeNumberStr = binding.editTextShelfLifeNumber.text.toString()

        if (productionDateStr.isNotEmpty() && shelfLifeNumberStr.isNotEmpty()) {
            try {
                val productionDate = DateUtils.parseDate(productionDateStr) ?: return
                val shelfLifeNumber = shelfLifeNumberStr.toInt()
                val selectedUnit = ShelfLifeUnit.values()[binding.spinnerShelfLifeUnit.selectedItemPosition]

                val shelfLifeDays = ProductStatusCalculator.calculateShelfLifeDays(shelfLifeNumber, selectedUnit.value)
                val expiryDate = ProductStatusCalculator.calculateExpiryDate(productionDate, shelfLifeDays)

                binding.editTextExpiryDate.setText(DateUtils.formatDate(expiryDate))
            } catch (e: Exception) {
                // 忽略计算错误
            }
        }
    }

    private fun saveProduct() {
        try {
            // 收集表单数据
            val name = binding.editTextProductName.text.toString().trim()
            val brand = binding.editTextBrand.text.toString().trim().takeIf { it.isNotEmpty() }
            val category = binding.editTextCategory.text.toString().trim().takeIf { it.isNotEmpty() }
            val barcode = binding.editTextBarcode.text.toString().trim().takeIf { it.isNotEmpty() }
            val productionDateStr = binding.editTextProductionDate.text.toString().trim()
            val expiryDateStr = binding.editTextExpiryDate.text.toString().trim()
            val stockDateStr = binding.editTextStockDate.text.toString().trim()
            val shelfLifeNumberStr = binding.editTextShelfLifeNumber.text.toString().trim()
            val quantityStr = binding.editTextQuantity.text.toString().trim()
            val warningDaysStr = binding.editTextWarningDays.text.toString().trim()
            val removalDaysStr = binding.editTextRemovalDays.text.toString().trim()
            val notes = binding.editTextNotes.text.toString().trim().takeIf { it.isNotEmpty() }
            val location = binding.editTextLocation.text.toString().trim().takeIf { it.isNotEmpty() }

            // 基本验证
            if (name.isEmpty()) {
                binding.editTextProductName.error = "商品名称不能为空"
                return
            }

            if (productionDateStr.isEmpty()) {
                binding.editTextProductionDate.error = "生产日期不能为空"
                return
            }

            if (expiryDateStr.isEmpty()) {
                binding.editTextExpiryDate.error = "到期日期不能为空"
                return
            }

            if (shelfLifeNumberStr.isEmpty()) {
                binding.editTextShelfLifeNumber.error = "保质期不能为空"
                return
            }

            // 解析日期和数值
            val productionDate = DateUtils.parseDate(productionDateStr) ?: run {
                binding.editTextProductionDate.error = "日期格式错误"
                return
            }

            val expiryDate = DateUtils.parseDate(expiryDateStr) ?: run {
                binding.editTextExpiryDate.error = "日期格式错误"
                return
            }

            val stockDate = DateUtils.parseDate(stockDateStr) ?: run {
                binding.editTextStockDate.error = "日期格式错误"
                return
            }

            val shelfLifeNumber = shelfLifeNumberStr.toIntOrNull() ?: run {
                binding.editTextShelfLifeNumber.error = "保质期必须是数字"
                return
            }

            val quantity = quantityStr.toIntOrNull() ?: run {
                binding.editTextQuantity.error = "数量必须是数字"
                return
            }

            val warningDays = warningDaysStr.toIntOrNull() ?: run {
                binding.editTextWarningDays.error = "提醒天数必须是数字"
                return
            }

            val removalDays = removalDaysStr.toIntOrNull() ?: run {
                binding.editTextRemovalDays.error = "下架天数必须是数字"
                return
            }

            // 获取选中的保质期单位
            val selectedUnit = ShelfLifeUnit.values()[binding.spinnerShelfLifeUnit.selectedItemPosition]
            val shelfLifeDays = ProductStatusCalculator.calculateShelfLifeDays(shelfLifeNumber, selectedUnit.value)

            // 创建Product对象
            val product = Product(
                name = name,
                brand = brand,
                category = category,
                barcode = barcode,
                productionDate = productionDate,
                expiryDate = expiryDate,
                stockDate = stockDate,
                shelfLifeDays = shelfLifeDays,
                shelfLifeNumber = shelfLifeNumber,
                shelfLifeUnit = selectedUnit.value,
                warningDays = warningDays,
                removalDays = removalDays,
                quantity = quantity,
                notes = notes,
                location = location
            )

            // 保存商品
            productViewModel.addProduct(product)

        } catch (e: Exception) {
            Snackbar.make(binding.root, "保存失败: ${e.message}", Snackbar.LENGTH_LONG).show()
        }
    }

    private fun clearForm() {
        binding.apply {
            editTextProductName.text?.clear()
            editTextBrand.text?.clear()
            editTextCategory.text?.clear()
            editTextBarcode.text?.clear()
            editTextProductionDate.text?.clear()
            editTextExpiryDate.text?.clear()
            editTextShelfLifeNumber.text?.clear()
            editTextQuantity.setText("1")
            editTextWarningDays.setText("3")
            editTextRemovalDays.setText("1")
            editTextNotes.text?.clear()
            editTextLocation.text?.clear()
            spinnerShelfLifeUnit.setSelection(0)
            editTextStockDate.setText(DateUtils.formatDate(System.currentTimeMillis()))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
