package com.example.dataassistant.ui

import androidx.test.espresso.Espresso.onView
import androidx.test.espresso.action.ViewActions.click
import androidx.test.espresso.assertion.ViewAssertions.matches
import androidx.test.espresso.matcher.ViewMatchers.*
import androidx.test.ext.junit.rules.ActivityScenarioRule
import androidx.test.ext.junit.runners.AndroidJUnit4
import androidx.test.filters.LargeTest
import com.example.dataassistant.MainActivity
import com.example.dataassistant.R
import org.junit.Rule
import org.junit.Test
import org.junit.runner.RunWith

/**
 * MainActivity UI测试
 */
@RunWith(AndroidJUnit4::class)
@LargeTest
class MainActivityTest {

    @get:Rule
    val activityRule = ActivityScenarioRule(MainActivity::class.java)

    @Test
    fun testMainActivityLaunch() {
        // 验证主界面是否正确启动
        onView(withId(R.id.nav_host_fragment_content_main))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testBottomNavigationVisible() {
        // 验证底部导航栏是否可见
        onView(withId(R.id.bottom_navigation))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testFabVisible() {
        // 验证FAB按钮是否可见
        onView(withId(R.id.fab))
            .check(matches(isDisplayed()))
    }

    @Test
    fun testNavigationToAddProduct() {
        // 测试导航到添加商品页面
        onView(withId(R.id.navigation_add))
            .perform(click())
        
        // 验证是否导航到了添加商品页面
        // 这里需要根据实际的添加商品页面布局来验证
        // onView(withId(R.id.add_product_layout))
        //     .check(matches(isDisplayed()))
    }

    @Test
    fun testNavigationToProductList() {
        // 测试导航到商品列表页面
        onView(withId(R.id.navigation_list))
            .perform(click())
        
        // 验证是否导航到了商品列表页面
        // onView(withId(R.id.product_list_layout))
        //     .check(matches(isDisplayed()))
    }

    @Test
    fun testNavigationToConfig() {
        // 测试导航到配置页面
        onView(withId(R.id.navigation_config))
            .perform(click())
        
        // 验证是否导航到了配置页面
        // onView(withId(R.id.config_layout))
        //     .check(matches(isDisplayed()))
    }

    @Test
    fun testFabClick() {
        // 测试FAB按钮点击
        onView(withId(R.id.fab))
            .perform(click())
        
        // 验证点击FAB后的行为（应该导航到添加商品页面）
        // onView(withId(R.id.add_product_layout))
        //     .check(matches(isDisplayed()))
    }

    @Test
    fun testToolbarVisible() {
        // 验证工具栏是否可见
        onView(withId(R.id.toolbar))
            .check(matches(isDisplayed()))
    }
}
