package com.example.dataassistant.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.lifecycleScope
import androidx.navigation.fragment.findNavController
import androidx.navigation.fragment.navArgs
import kotlinx.coroutines.launch
import com.example.dataassistant.R
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.databinding.FragmentProductDetailBinding
import com.example.dataassistant.ui.viewmodel.ProductViewModel
import com.example.dataassistant.utils.DateUtils
import com.example.dataassistant.utils.ProductStatusCalculator
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint

/**
 * 商品详情Fragment
 */
@AndroidEntryPoint
class ProductDetailFragment : Fragment() {

    private var _binding: FragmentProductDetailBinding? = null
    private val binding get() = _binding!!

    private val args: ProductDetailFragmentArgs by navArgs()
    private val viewModel: ProductViewModel by viewModels()

    private var currentProduct: Product? = null

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductDetailBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupMenu()
        setupObservers()
        setupClickListeners()

        // 加载商品数据
        loadProduct()
    }

    private fun setupMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_product_detail, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_edit -> {
                        navigateToEdit()
                        true
                    }
                    R.id.action_delete -> {
                        showDeleteConfirmDialog()
                        true
                    }
                    R.id.action_share -> {
                        shareProduct()
                        true
                    }
                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    private fun setupObservers() {
        // 观察UI状态
        viewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            binding.progressBar.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE

            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }

            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                viewModel.clearMessage()
                // 如果是删除成功，返回上一页
                if (message.contains("删除成功")) {
                    findNavController().navigateUp()
                }
            }
        }
    }

    private fun setupClickListeners() {
        // 编辑按钮
        binding.buttonEdit.setOnClickListener {
            navigateToEdit()
        }

        // 删除按钮
        binding.buttonDelete.setOnClickListener {
            showDeleteConfirmDialog()
        }
    }

    private fun loadProduct() {
        viewLifecycleOwner.lifecycleScope.launch {
            try {
                val product = viewModel.getProductById(args.productId)
                if (product != null) {
                    currentProduct = product
                    displayProduct(product)
                } else {
                    Snackbar.make(binding.root, "商品不存在", Snackbar.LENGTH_LONG).show()
                    findNavController().navigateUp()
                }
            } catch (e: Exception) {
                Snackbar.make(binding.root, "加载商品失败: ${e.message}", Snackbar.LENGTH_LONG).show()
                findNavController().navigateUp()
            }
        }
    }

    private fun displayProduct(product: Product) {
        binding.apply {
            // 基本信息
            textViewProductName.text = product.name
            textViewBrand.text = product.brand ?: "未知品牌"
            textViewCategory.text = product.category ?: "未分类"
            textViewBarcode.text = product.barcode ?: "无条码"
            textViewQuantity.text = "数量: ${product.quantity}"
            textViewLocation.text = "位置: ${product.location ?: "未设置"}"

            // 日期信息
            textViewProductionDate.text = "生产日期: ${DateUtils.formatDate(product.productionDate)}"
            textViewExpiryDate.text = "到期日期: ${DateUtils.formatDate(product.expiryDate)}"
            textViewStockDate.text = "入库日期: ${DateUtils.formatDate(product.stockDate)}"

            // 保质期信息
            textViewShelfLife.text = "保质期: ${product.shelfLifeNumber}${product.shelfLifeUnit}"
            textViewShelfLifeDays.text = "保质期天数: ${product.shelfLifeDays}天"

            // 提醒设置
            textViewWarningDays.text = "临期提醒: ${product.warningDays}天"
            textViewRemovalDays.text = "下架提醒: ${product.removalDays}天"

            // 状态信息
            val statusInfo = ProductStatusCalculator.calculateProductStatus(product)
            textViewStatus.text = statusInfo.statusText
            textViewRemainingDays.text = DateUtils.formatRemainingTime(statusInfo.remainingDays)

            // 设置状态颜色
            val statusColor = android.graphics.Color.parseColor(statusInfo.statusColor)
            textViewStatus.setTextColor(statusColor)
            viewStatusIndicator.setBackgroundColor(statusColor)

            // 备注
            if (!product.notes.isNullOrBlank()) {
                textViewNotes.text = product.notes
                textViewNotes.visibility = View.VISIBLE
                textViewNotesLabel.visibility = View.VISIBLE
            } else {
                textViewNotes.visibility = View.GONE
                textViewNotesLabel.visibility = View.GONE
            }

            // 时间戳
            textViewCreateTime.text = "创建时间: ${DateUtils.formatDate(product.createTime)}"
            if (product.updateTime != product.createTime) {
                textViewUpdateTime.text = "更新时间: ${DateUtils.formatDate(product.updateTime)}"
                textViewUpdateTime.visibility = View.VISIBLE
            } else {
                textViewUpdateTime.visibility = View.GONE
            }
        }
    }

    private fun navigateToEdit() {
        currentProduct?.let { product ->
            val action = ProductDetailFragmentDirections.actionProductDetailToProductEdit(product.id)
            findNavController().navigate(action)
        }
    }

    private fun showDeleteConfirmDialog() {
        currentProduct?.let { product ->
            MaterialAlertDialogBuilder(requireContext())
                .setTitle("删除商品")
                .setMessage("确定要删除「${product.name}」吗？此操作不可撤销。")
                .setPositiveButton("删除") { _, _ ->
                    viewModel.deleteProduct(product)
                }
                .setNegativeButton("取消", null)
                .show()
        }
    }

    private fun shareProduct() {
        currentProduct?.let { product ->
            val shareText = buildString {
                appendLine("商品信息")
                appendLine("名称: ${product.name}")
                appendLine("品牌: ${product.brand ?: "未知"}")
                appendLine("分类: ${product.category ?: "未分类"}")
                appendLine("生产日期: ${DateUtils.formatDate(product.productionDate)}")
                appendLine("到期日期: ${DateUtils.formatDate(product.expiryDate)}")
                appendLine("保质期: ${product.shelfLifeNumber}${product.shelfLifeUnit}")

                val statusInfo = ProductStatusCalculator.calculateProductStatus(product)
                appendLine("状态: ${statusInfo.statusText}")

                if (!product.notes.isNullOrBlank()) {
                    appendLine("备注: ${product.notes}")
                }
            }

            val shareIntent = android.content.Intent().apply {
                action = android.content.Intent.ACTION_SEND
                type = "text/plain"
                putExtra(android.content.Intent.EXTRA_TEXT, shareText)
                putExtra(android.content.Intent.EXTRA_SUBJECT, "商品信息 - ${product.name}")
            }

            startActivity(android.content.Intent.createChooser(shareIntent, "分享商品信息"))
        }
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
