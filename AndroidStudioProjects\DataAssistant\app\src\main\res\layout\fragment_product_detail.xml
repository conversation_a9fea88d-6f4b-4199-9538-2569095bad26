<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 状态指示器 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:gravity="center_vertical"
            android:layout_marginBottom="16dp">

            <View
                android:id="@+id/view_status_indicator"
                android:layout_width="8dp"
                android:layout_height="40dp"
                android:layout_marginEnd="12dp"
                android:background="@android:color/holo_green_light" />

            <LinearLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:orientation="vertical">

                <TextView
                    android:id="@+id/text_view_status"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="正常"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    tools:text="临期" />

                <TextView
                    android:id="@+id/text_view_remaining_days"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="还有7天"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    tools:text="还有3天" />

            </LinearLayout>

        </LinearLayout>

        <!-- 基本信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="基本信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_product_name"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="商品名称"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="8dp"
                    tools:text="新希望低温牛奶" />

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="品牌: "
                        android:textSize="14sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/text_view_brand"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="未知品牌"
                        android:textSize="14sp"
                        tools:text="新希望" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="分类: "
                        android:textSize="14sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/text_view_category"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="未分类"
                        android:textSize="14sp"
                        tools:text="低温奶" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:layout_marginBottom="4dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="条码: "
                        android:textSize="14sp"
                        android:textColor="@android:color/darker_gray" />

                    <TextView
                        android:id="@+id/text_view_barcode"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="无条码"
                        android:textSize="14sp"
                        tools:text="6901028089685" />

                </LinearLayout>

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal">

                    <TextView
                        android:id="@+id/text_view_quantity"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="数量: 1"
                        android:textSize="14sp"
                        tools:text="数量: 2" />

                    <TextView
                        android:id="@+id/text_view_location"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="位置: 未设置"
                        android:textSize="14sp"
                        tools:text="位置: 冰箱" />

                </LinearLayout>

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 日期信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="日期信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_production_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="生产日期: 2024-01-01"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    tools:text="生产日期: 2024-01-01" />

                <TextView
                    android:id="@+id/text_view_expiry_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="到期日期: 2024-01-15"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    tools:text="到期日期: 2024-01-15" />

                <TextView
                    android:id="@+id/text_view_stock_date"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="入库日期: 2024-01-01"
                    android:textSize="14sp"
                    tools:text="入库日期: 2024-01-01" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 保质期信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="保质期信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_shelf_life"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保质期: 15天"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    tools:text="保质期: 15天" />

                <TextView
                    android:id="@+id/text_view_shelf_life_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="保质期天数: 15天"
                    android:textSize="14sp"
                    tools:text="保质期天数: 15天" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 提醒设置卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="提醒设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_warning_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="临期提醒: 3天"
                    android:textSize="14sp"
                    android:layout_marginBottom="4dp"
                    tools:text="临期提醒: 3天" />

                <TextView
                    android:id="@+id/text_view_removal_days"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="下架提醒: 1天"
                    android:textSize="14sp"
                    tools:text="下架提醒: 1天" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 备注信息卡片 -->
        <com.google.android.material.card.MaterialCardView
            android:id="@+id/card_notes"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/text_view_notes_label"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="备注信息"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_notes"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="无备注"
                    android:textSize="14sp"
                    tools:text="这是一个备注信息" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 时间戳信息 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:layout_marginBottom="16dp">

            <TextView
                android:id="@+id/text_view_create_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="创建时间: 2024-01-01"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:layout_marginBottom="2dp"
                tools:text="创建时间: 2024-01-01 10:30" />

            <TextView
                android:id="@+id/text_view_update_time"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="更新时间: 2024-01-01"
                android:textSize="12sp"
                android:textColor="@android:color/darker_gray"
                android:visibility="gone"
                tools:text="更新时间: 2024-01-01 15:45"
                tools:visibility="visible" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/button_edit"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="编辑" />

            <Button
                android:id="@+id/button_delete"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="删除"
                android:textColor="@android:color/holo_red_dark" />

        </LinearLayout>

        <!-- 加载指示器 -->
        <ProgressBar
            android:id="@+id/progress_bar"
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:layout_gravity="center"
            android:layout_marginTop="16dp"
            android:visibility="gone" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
