<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,192]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/toolbar" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,192]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/nav_host_fragment_content_main" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/nav_host_fragment_content_main" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.ScrollView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,240][1232,634]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,240][1232,634]" drawing-order="1" hint=""><node index="0" text="条码信息" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,288][312,367]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,403][1184,586]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,403][877,586]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,419][877,586]" drawing-order="1" hint=""><node index="0" text="商品条码" resource-id="com.example.dataassistant:id/edit_text_barcode" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,419][877,586]" drawing-order="1" hint="商品条码" /></node></node><node index="1" text="扫码" resource-id="com.example.dataassistant:id/button_scan_barcode" class="android.widget.Button" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[901,431][1184,575]" drawing-order="2" hint="" /></node></node></node><node index="1" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,682][1232,1466]" drawing-order="4" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,682][1232,1466]" drawing-order="1" hint=""><node index="0" text="基本信息" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,730][312,809]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,845][1184,1028]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,861][1184,1028]" drawing-order="1" hint=""><node index="0" text="商品名称 *" resource-id="com.example.dataassistant:id/edit_text_product_name" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,861][1184,1028]" drawing-order="1" hint="商品名称 *" /></node></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1052][1184,1235]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1052][616,1235]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1068][616,1235]" drawing-order="1" hint=""><node index="0" text="品牌" resource-id="com.example.dataassistant:id/edit_text_brand" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,1068][616,1235]" drawing-order="1" hint="品牌" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1052][1184,1235]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1068][1184,1235]" drawing-order="1" hint=""><node index="0" text="分类" resource-id="com.example.dataassistant:id/edit_text_category" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[664,1068][1184,1235]" drawing-order="1" hint="分类" /></node></node></node><node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1235][1184,1418]" drawing-order="4" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1235][616,1418]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1251][616,1418]" drawing-order="1" hint=""><node index="0" text="1" resource-id="com.example.dataassistant:id/edit_text_quantity" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,1251][616,1418]" drawing-order="1" hint="数量" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1235][1184,1418]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1251][1184,1418]" drawing-order="1" hint=""><node index="0" text="存放位置" resource-id="com.example.dataassistant:id/edit_text_location" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[664,1251][1184,1418]" drawing-order="1" hint="存放位置" /></node></node></node></node></node><node index="2" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,1514][1232,2440]" drawing-order="5" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,1514][1232,2440]" drawing-order="1" hint=""><node index="0" text="日期信息" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1562][312,1641]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1677][1184,1860]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1677][616,1860]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1693][616,1860]" drawing-order="1" hint=""><node index="0" text="生产日期 *" resource-id="com.example.dataassistant:id/edit_text_production_date" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,1693][616,1860]" drawing-order="1" hint="生产日期 *" /></node></node><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1677][1184,1860]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,1693][1184,1860]" drawing-order="1" hint=""><node index="0" text="2025-08-16" resource-id="com.example.dataassistant:id/edit_text_stock_date" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[664,1693][1184,1860]" drawing-order="1" hint="入库日期" /></node></node></node><node index="2" text="保质期设置" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1908][336,1978]" drawing-order="3" hint="" /><node index="3" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2002][1184,2185]" drawing-order="4" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2002][616,2185]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2018][616,2185]" drawing-order="1" hint=""><node index="0" text="保质期数量 *" resource-id="com.example.dataassistant:id/edit_text_shelf_life_number" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,2018][616,2185]" drawing-order="1" hint="保质期数量 *" /></node></node><node index="1" text="" resource-id="com.example.dataassistant:id/spinner_shelf_life_unit" class="android.widget.Spinner" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="true" long-clickable="true" password="false" selected="false" bounds="[664,2087][1184,2159]" drawing-order="2" hint=""><node index="0" text="天" resource-id="android:id/text1" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,2088][1040,2158]" drawing-order="1" hint="" /></node></node><node index="4" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2209][1184,2392]" drawing-order="5" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2225][1184,2392]" drawing-order="1" hint=""><node index="0" text="到期日期（自动计算）" resource-id="com.example.dataassistant:id/edit_text_expiry_date" class="android.widget.EditText" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="true" password="false" selected="false" bounds="[96,2225][1184,2392]" drawing-order="1" hint="到期日期（自动计算）" /></node></node></node></node><node index="3" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,2488][1232,2664]" drawing-order="6" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,2488][1232,2664]" drawing-order="1" hint=""><node index="0" text="提醒设置" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2536][312,2615]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2651][1184,2664]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,2651][616,2664]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[664,2651][1184,2664]" drawing-order="2" hint="" /></node></node></node></node></node></node></node><node index="2" text="" resource-id="com.example.dataassistant:id/bottom_navigation" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2544][1280,2856]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2544][1280,2784]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_home" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="首页" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2544][320,2784]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[64,2616][256,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[124,2628][196,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[123,2731][197,2784]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_add" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="添加商品" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[320,2544][640,2784]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[384,2580][576,2676]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_active_indicator_view" class="android.view.View" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[384,2580][576,2676]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[444,2592][516,2664]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[405,2694][554,2784]" drawing-order="2" hint=""><node index="0" text="添加商品" resource-id="com.example.dataassistant:id/navigation_bar_item_large_label_view" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[405,2694][554,2747]" drawing-order="2" hint="" /></node></node><node index="2" text="" resource-id="com.example.dataassistant:id/navigation_list" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="商品列表" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[640,2544][960,2784]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[704,2616][896,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[764,2628][836,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[725,2731][874,2784]" drawing-order="2" hint="" /></node><node index="3" text="" resource-id="com.example.dataassistant:id/navigation_config" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="配置管理" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[960,2544][1280,2784]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1024,2616][1216,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1084,2628][1156,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1045,2731][1194,2784]" drawing-order="2" hint="" /></node></node></node><node NAF="true" index="3" text="" resource-id="com.example.dataassistant:id/fab" class="android.widget.ImageButton" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1064,2472][1232,2640]" drawing-order="4" hint="" /></node></node></node></node></node></node></hierarchy>