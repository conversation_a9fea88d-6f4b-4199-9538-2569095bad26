package com.example.dataassistant.ui

import android.os.Bundle
import androidx.activity.viewModels
import androidx.appcompat.app.AppCompatActivity
import androidx.navigation.fragment.NavHostFragment
import androidx.navigation.ui.setupWithNavController
import com.example.dataassistant.R
import com.example.dataassistant.databinding.ActivityMainBinding
import com.example.dataassistant.ui.viewmodel.ProductViewModel
import dagger.hilt.android.AndroidEntryPoint

/**
 * 主活动
 */
@AndroidEntryPoint
class MainActivity : AppCompatActivity() {

    private lateinit var binding: ActivityMainBinding
    private val productViewModel: ProductViewModel by viewModels()

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        binding = ActivityMainBinding.inflate(layoutInflater)
        setContentView(binding.root)

        // 添加调试日志
        android.util.Log.d("MainActivity", "onCreate called")

        setupNavigation()
        observeViewModel()

        android.util.Log.d("MainActivity", "onCreate completed")
    }

    private fun setupNavigation() {
        android.util.Log.d("MainActivity", "setupNavigation called")

        val navHostFragment = supportFragmentManager
            .findFragmentById(R.id.nav_host_fragment_content_main) as? NavHostFragment

        if (navHostFragment == null) {
            android.util.Log.e("MainActivity", "NavHostFragment not found!")
            return
        }

        val navController = navHostFragment.navController
        android.util.Log.d("MainActivity", "NavController obtained: $navController")

        binding.bottomNavigation.setupWithNavController(navController)
        android.util.Log.d("MainActivity", "setupNavigation completed")
    }

    private fun observeViewModel() {
        // 加载商品统计信息
        productViewModel.loadStatistics()

        // 观察UI状态
        productViewModel.uiState.observe(this) { uiState ->
            // 处理错误和消息
            uiState.error?.let { error ->
                // 显示错误信息
                showError(error)
                productViewModel.clearError()
            }

            uiState.message?.let { message ->
                // 显示成功信息
                showMessage(message)
                productViewModel.clearMessage()
            }
        }
    }

    private fun showError(message: String) {
        // TODO: 实现错误提示
    }

    private fun showMessage(message: String) {
        // TODO: 实现成功提示
    }
}
