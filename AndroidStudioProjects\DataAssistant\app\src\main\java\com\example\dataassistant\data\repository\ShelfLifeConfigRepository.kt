package com.example.dataassistant.data.repository

import androidx.lifecycle.LiveData
import com.example.dataassistant.data.dao.ShelfLifeConfigDao
import com.example.dataassistant.data.entity.ShelfLifeConfig
import javax.inject.Inject
import javax.inject.Singleton

/**
 * 保质期配置数据仓库
 */
@Singleton
class ShelfLifeConfigRepository @Inject constructor(
    private val shelfLifeConfigDao: ShelfLifeConfigDao
) {

    /**
     * 获取所有配置
     */
    fun getAllConfigs(): LiveData<List<ShelfLifeConfig>> = shelfLifeConfigDao.getAllConfigs()

    /**
     * 获取启用的配置
     */
    fun getEnabledConfigs(): LiveData<List<ShelfLifeConfig>> = shelfLifeConfigDao.getEnabledConfigs()

    /**
     * 获取默认配置
     */
    suspend fun getDefaultConfig(): ShelfLifeConfig? = shelfLifeConfigDao.getDefaultConfig()

    /**
     * 根据ID获取配置
     */
    suspend fun getConfigById(id: String): ShelfLifeConfig? = shelfLifeConfigDao.getConfigById(id)

    /**
     * 根据名称搜索配置
     */
    suspend fun searchConfigs(keyword: String): List<ShelfLifeConfig> =
        shelfLifeConfigDao.searchConfigs(keyword)

    /**
     * 插入配置
     */
    suspend fun insertConfig(config: ShelfLifeConfig) = shelfLifeConfigDao.insertConfig(config)

    /**
     * 批量插入配置
     */
    suspend fun insertConfigs(configs: List<ShelfLifeConfig>) =
        shelfLifeConfigDao.insertConfigs(configs)

    /**
     * 更新配置
     */
    suspend fun updateConfig(config: ShelfLifeConfig) = shelfLifeConfigDao.updateConfig(config)

    /**
     * 删除配置
     */
    suspend fun deleteConfig(config: ShelfLifeConfig) = shelfLifeConfigDao.deleteConfig(config)

    /**
     * 根据ID删除配置
     */
    suspend fun deleteConfigById(id: String) = shelfLifeConfigDao.deleteConfigById(id)

    /**
     * 设置默认配置
     */
    suspend fun setDefaultConfig(configId: String) = shelfLifeConfigDao.setDefaultConfig(configId)

    /**
     * 启用/禁用配置
     */
    suspend fun updateConfigEnabled(id: String, enabled: Boolean) =
        shelfLifeConfigDao.updateConfigEnabled(id, enabled, System.currentTimeMillis())



    /**
     * 获取配置总数
     */
    suspend fun getConfigCount(): Int = shelfLifeConfigDao.getConfigCount()

    /**
     * 清空所有配置
     */
    suspend fun deleteAllConfigs() = shelfLifeConfigDao.deleteAllConfigs()

    /**
     * 创建默认配置
     */
    suspend fun createDefaultConfigs() {
        val defaultConfigs = listOf(
            ShelfLifeConfig(
                name = "短保质期商品",
                description = "适用于保质期较短的商品，如面包、牛奶等",
                minShelfLifeDays = 1,
                maxShelfLifeDays = 30,
                warningDays = 3,
                removalDays = 1,
                discountDays = 5,
                isDefault = true
            ),
            ShelfLifeConfig(
                name = "中等保质期商品",
                description = "适用于保质期中等的商品，如罐头、调料等",
                minShelfLifeDays = 31,
                maxShelfLifeDays = 365,
                warningDays = 7,
                removalDays = 3,
                discountDays = 14
            ),
            ShelfLifeConfig(
                name = "长保质期商品",
                description = "适用于保质期较长的商品，如干货、冷冻食品等",
                minShelfLifeDays = 366,
                maxShelfLifeDays = Int.MAX_VALUE,
                warningDays = 30,
                removalDays = 7,
                discountDays = 60
            )
        )

        insertConfigs(defaultConfigs)
    }
}
