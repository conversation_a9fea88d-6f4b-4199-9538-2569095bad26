<?xml version="1.0" encoding="utf-8"?>
<androidx.swiperefreshlayout.widget.SwipeRefreshLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:id="@+id/swipe_refresh_layout"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <androidx.core.widget.NestedScrollView
        android:layout_width="match_parent"
        android:layout_height="match_parent"
        android:fillViewport="true">

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:padding="12dp">

            <!-- 欢迎标题 -->
            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="horizontal"
                android:gravity="center_vertical"
                android:layout_marginBottom="16dp">

                <TextView
                    android:layout_width="0dp"
                    android:layout_height="wrap_content"
                    android:layout_weight="1"
                    android:text="保质期管理助手"
                    android:textSize="20sp"
                    android:textStyle="bold"
                    android:textColor="@color/text_primary" />

                <Button
                    android:id="@+id/button_date_calculator"
                    style="@style/Widget.Material3.Button.IconButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:contentDescription="日期计算器"
                    app:icon="@android:drawable/ic_menu_my_calendar" />

            </LinearLayout>

            <!-- 统计信息卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="商品概览"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="3">

                        <!-- 总商品数 -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_total_products"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="1dp"
                            app:cardBackgroundColor="@color/primary">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="12dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_view_total_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="总商品"
                                    android:textSize="11sp"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <!-- 临期商品 -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_warning_products"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="3dp"
                            android:layout_marginEnd="3dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="1dp"
                            app:cardBackgroundColor="@color/status_warning">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="12dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_view_warning_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="临期"
                                    android:textSize="11sp"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                        <!-- 过期商品 -->
                        <com.google.android.material.card.MaterialCardView
                            android:id="@+id/card_expired_products"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            app:cardCornerRadius="12dp"
                            app:cardElevation="1dp"
                            app:cardBackgroundColor="@color/status_expired">

                            <LinearLayout
                                android:layout_width="match_parent"
                                android:layout_height="wrap_content"
                                android:orientation="vertical"
                                android:padding="12dp"
                                android:gravity="center">

                                <TextView
                                    android:id="@+id/text_view_expired_count"
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="0"
                                    android:textSize="20sp"
                                    android:textStyle="bold"
                                    android:textColor="@color/white" />

                                <TextView
                                    android:layout_width="wrap_content"
                                    android:layout_height="wrap_content"
                                    android:text="过期"
                                    android:textSize="11sp"
                                    android:textColor="@color/white" />

                            </LinearLayout>

                        </com.google.android.material.card.MaterialCardView>

                    </LinearLayout>

                    <!-- 健康度指示器 -->
                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:layout_marginTop="12dp"
                        android:gravity="center_vertical">

                        <TextView
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="库存健康度"
                            android:textSize="13sp"
                            android:textColor="@color/text_secondary" />

                        <ProgressBar
                            android:id="@+id/progress_bar_health"
                            style="?android:attr/progressBarStyleHorizontal"
                            android:layout_width="0dp"
                            android:layout_height="8dp"
                            android:layout_weight="1"
                            android:layout_marginStart="8dp"
                            android:layout_marginEnd="8dp"
                            android:max="100"
                            android:progressTint="@color/status_normal" />

                        <TextView
                            android:id="@+id/text_view_health_percentage"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="100%"
                            android:textSize="13sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 提醒卡片 -->
            <com.google.android.material.card.MaterialCardView
                android:id="@+id/card_alert"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                android:visibility="gone"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/status_warning_light">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:padding="12dp"
                    android:gravity="center_vertical">

                    <ImageView
                        android:layout_width="20dp"
                        android:layout_height="20dp"
                        android:src="@android:drawable/ic_dialog_alert"
                        android:layout_marginEnd="8dp"
                        android:tint="@color/status_warning" />

                    <TextView
                        android:id="@+id/text_view_alert_message"
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="您有商品即将过期"
                        android:textColor="@color/status_warning"
                        android:textSize="13sp" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 快捷操作 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:layout_marginBottom="12dp"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <TextView
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content"
                        android:text="快捷操作"
                        android:textSize="16sp"
                        android:textStyle="bold"
                        android:textColor="@color/text_primary"
                        android:layout_marginBottom="12dp" />

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:weightSum="2">

                        <Button
                            android:id="@+id/button_add_product"
                            style="@style/Widget.Material3.Button"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginEnd="6dp"
                            android:text="添加商品"
                            app:icon="@drawable/ic_add_24" />

                        <Button
                            android:id="@+id/button_view_list"
                            style="@style/Widget.Material3.Button.OutlinedButton"
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:layout_marginStart="6dp"
                            android:text="查看列表"
                            app:icon="@drawable/ic_list_24" />

                    </LinearLayout>

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 最近添加的商品 -->
            <com.google.android.material.card.MaterialCardView
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                app:cardCornerRadius="16dp"
                app:cardElevation="2dp"
                app:cardBackgroundColor="@color/card_background">

                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="vertical"
                    android:padding="16dp">

                    <LinearLayout
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:orientation="horizontal"
                        android:gravity="center_vertical"
                        android:layout_marginBottom="12dp">

                        <TextView
                            android:layout_width="0dp"
                            android:layout_height="wrap_content"
                            android:layout_weight="1"
                            android:text="最近添加"
                            android:textSize="16sp"
                            android:textStyle="bold"
                            android:textColor="@color/text_primary" />

                        <Button
                            android:id="@+id/button_view_more"
                            style="@style/Widget.Material3.Button.TextButton"
                            android:layout_width="wrap_content"
                            android:layout_height="wrap_content"
                            android:text="查看更多"
                            android:textSize="12sp" />

                    </LinearLayout>

                    <androidx.recyclerview.widget.RecyclerView
                        android:id="@+id/recycler_view_recent_products"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:nestedScrollingEnabled="false"
                        tools:listitem="@layout/item_product" />

                    <TextView
                        android:id="@+id/text_view_empty_state"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:text="暂无商品数据\n点击上方按钮添加第一个商品"
                        android:textAlignment="center"
                        android:textSize="14sp"
                        android:textColor="@color/text_hint"
                        android:padding="24dp"
                        android:visibility="gone" />

                </LinearLayout>

            </com.google.android.material.card.MaterialCardView>

            <!-- 加载指示器 -->
            <ProgressBar
                android:id="@+id/progress_bar"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center"
                android:layout_marginTop="32dp"
                android:visibility="gone" />

        </LinearLayout>

    </androidx.core.widget.NestedScrollView>

</androidx.swiperefreshlayout.widget.SwipeRefreshLayout>
