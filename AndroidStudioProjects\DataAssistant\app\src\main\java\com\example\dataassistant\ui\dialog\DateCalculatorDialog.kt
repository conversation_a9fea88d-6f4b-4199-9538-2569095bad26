package com.example.dataassistant.ui.dialog

import android.app.Dialog
import android.os.Bundle
import android.view.LayoutInflater
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.fragment.app.DialogFragment
import com.example.dataassistant.R
import com.example.dataassistant.databinding.DialogDateCalculatorBinding
import com.example.dataassistant.utils.DateUtils
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import java.text.SimpleDateFormat
import java.util.*

/**
 * 日期计算器对话框
 */
class DateCalculatorDialog : DialogFragment() {

    private var _binding: DialogDateCalculatorBinding? = null
    private val binding get() = _binding!!

    private val dateFormat = SimpleDateFormat("yyyy-MM-dd", Locale.getDefault())

    companion object {
        fun newInstance(): DateCalculatorDialog {
            return DateCalculatorDialog()
        }
    }

    override fun onCreateDialog(savedInstanceState: Bundle?): Dialog {
        _binding = DialogDateCalculatorBinding.inflate(layoutInflater)
        
        setupViews()
        setupClickListeners()
        
        return MaterialAlertDialogBuilder(requireContext())
            .setTitle("日期计算器")
            .setView(binding.root)
            .setPositiveButton("关闭", null)
            .create()
    }

    private fun setupViews() {
        // 设置默认生产日期为今天
        val today = Calendar.getInstance()
        binding.editTextProductionDate.setText(dateFormat.format(today.time))

        // 设置保质期单位选择器
        val units = arrayOf("天", "周", "月", "年")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, units)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerShelfLifeUnit.adapter = adapter

        // 设置快捷保质期按钮
        setupQuickShelfLifeButtons()
    }

    private fun setupQuickShelfLifeButtons() {
        binding.buttonQuick7Days.setOnClickListener { setQuickShelfLife(7, 0) }
        binding.buttonQuick15Days.setOnClickListener { setQuickShelfLife(15, 0) }
        binding.buttonQuick1Month.setOnClickListener { setQuickShelfLife(1, 2) }
        binding.buttonQuick3Months.setOnClickListener { setQuickShelfLife(3, 2) }
        binding.buttonQuick6Months.setOnClickListener { setQuickShelfLife(6, 2) }
        binding.buttonQuick1Year.setOnClickListener { setQuickShelfLife(1, 3) }
    }

    private fun setQuickShelfLife(number: Int, unitIndex: Int) {
        binding.editTextShelfLifeNumber.setText(number.toString())
        binding.spinnerShelfLifeUnit.setSelection(unitIndex)
        calculateDates()
    }

    private fun setupClickListeners() {
        // 生产日期选择
        binding.editTextProductionDate.setOnClickListener {
            showDatePicker { date ->
                binding.editTextProductionDate.setText(dateFormat.format(date))
                calculateDates()
            }
        }

        // 计算按钮
        binding.buttonCalculate.setOnClickListener {
            calculateDates()
        }

        // 重置按钮
        binding.buttonReset.setOnClickListener {
            resetForm()
        }

        // 保质期数量变化监听
        binding.editTextShelfLifeNumber.setOnFocusChangeListener { _, _ ->
            calculateDates()
        }

        // 保质期单位变化监听
        binding.spinnerShelfLifeUnit.setOnItemSelectedListener(object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                calculateDates()
            }
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        })
    }

    private fun showDatePicker(onDateSelected: (Date) -> Unit) {
        val calendar = Calendar.getInstance()
        val year = calendar.get(Calendar.YEAR)
        val month = calendar.get(Calendar.MONTH)
        val day = calendar.get(Calendar.DAY_OF_MONTH)

        val datePickerDialog = android.app.DatePickerDialog(
            requireContext(),
            { _, selectedYear, selectedMonth, selectedDay ->
                calendar.set(selectedYear, selectedMonth, selectedDay)
                onDateSelected(calendar.time)
            },
            year, month, day
        )
        datePickerDialog.show()
    }

    private fun calculateDates() {
        val productionDateStr = binding.editTextProductionDate.text.toString()
        val shelfLifeNumberStr = binding.editTextShelfLifeNumber.text.toString()
        val unitIndex = binding.spinnerShelfLifeUnit.selectedItemPosition

        if (productionDateStr.isEmpty() || shelfLifeNumberStr.isEmpty()) {
            clearResults()
            return
        }

        try {
            val productionDate = dateFormat.parse(productionDateStr) ?: return
            val shelfLifeNumber = shelfLifeNumberStr.toInt()
            
            // 转换为天数
            val shelfLifeDays = when (unitIndex) {
                0 -> shelfLifeNumber // 天
                1 -> shelfLifeNumber * 7 // 周
                2 -> shelfLifeNumber * 30 // 月
                3 -> shelfLifeNumber * 365 // 年
                else -> shelfLifeNumber
            }

            // 计算到期日期
            val calendar = Calendar.getInstance()
            calendar.time = productionDate
            calendar.add(Calendar.DAY_OF_YEAR, shelfLifeDays)
            val expiryDate = calendar.time

            // 计算临期日期（到期前7天）
            calendar.add(Calendar.DAY_OF_YEAR, -7)
            val warningDate = calendar.time

            // 计算下架日期（到期前3天）
            calendar.time = expiryDate
            calendar.add(Calendar.DAY_OF_YEAR, -3)
            val removalDate = calendar.time

            // 计算剩余天数
            val today = Calendar.getInstance().time
            val remainingDays = DateUtils.daysBetween(today, expiryDate)

            // 显示结果
            showResults(expiryDate, warningDate, removalDate, remainingDays, shelfLifeDays)

        } catch (e: Exception) {
            clearResults()
        }
    }

    private fun showResults(expiryDate: Date, warningDate: Date, removalDate: Date, remainingDays: Int, shelfLifeDays: Int) {
        binding.layoutResults.visibility = View.VISIBLE
        
        binding.textViewExpiryDate.text = "到期日期：${dateFormat.format(expiryDate)}"
        binding.textViewWarningDate.text = "临期提醒：${dateFormat.format(warningDate)}"
        binding.textViewRemovalDate.text = "下架提醒：${dateFormat.format(removalDate)}"
        binding.textViewShelfLifeDays.text = "保质期：${shelfLifeDays}天"
        
        // 设置状态和颜色
        when {
            remainingDays < 0 -> {
                binding.textViewRemainingDays.text = "已过期 ${-remainingDays} 天"
                binding.textViewRemainingDays.setTextColor(requireContext().getColor(R.color.status_expired))
            }
            remainingDays <= 3 -> {
                binding.textViewRemainingDays.text = "剩余 ${remainingDays} 天（需下架）"
                binding.textViewRemainingDays.setTextColor(requireContext().getColor(R.color.status_removal))
            }
            remainingDays <= 7 -> {
                binding.textViewRemainingDays.text = "剩余 ${remainingDays} 天（临期）"
                binding.textViewRemainingDays.setTextColor(requireContext().getColor(R.color.status_warning))
            }
            else -> {
                binding.textViewRemainingDays.text = "剩余 ${remainingDays} 天"
                binding.textViewRemainingDays.setTextColor(requireContext().getColor(R.color.status_normal))
            }
        }
    }

    private fun clearResults() {
        binding.layoutResults.visibility = View.GONE
    }

    private fun resetForm() {
        val today = Calendar.getInstance()
        binding.editTextProductionDate.setText(dateFormat.format(today.time))
        binding.editTextShelfLifeNumber.setText("")
        binding.spinnerShelfLifeUnit.setSelection(0)
        clearResults()
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
