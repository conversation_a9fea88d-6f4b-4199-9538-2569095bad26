package com.example.dataassistant.notification

import android.content.Context
import androidx.work.*
import com.example.dataassistant.worker.NotificationWorker
import java.util.concurrent.TimeUnit

/**
 * 通知管理器
 */
class NotificationManager(private val context: Context) {

    companion object {
        private const val DEFAULT_NOTIFICATION_HOUR = 9
        private const val DEFAULT_NOTIFICATION_MINUTE = 0
        private const val DEFAULT_REPEAT_INTERVAL_HOURS = 24L
    }

    private val workManager = WorkManager.getInstance(context)

    /**
     * 启动定期通知检查
     */
    fun startPeriodicNotifications(
        intervalHours: Long = DEFAULT_REPEAT_INTERVAL_HOURS,
        notificationHour: Int = DEFAULT_NOTIFICATION_HOUR,
        notificationMinute: Int = DEFAULT_NOTIFICATION_MINUTE
    ) {
        // 取消现有的工作
        cancelPeriodicNotifications()

        // 计算初始延迟时间
        val initialDelay = calculateInitialDelay(notificationHour, notificationMinute)

        // 创建约束条件
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .setRequiresBatteryNotLow(false)
            .setRequiresCharging(false)
            .setRequiresDeviceIdle(false)
            .setRequiresStorageNotLow(false)
            .build()

        // 创建周期性工作请求
        val notificationWork = PeriodicWorkRequestBuilder<NotificationWorker>(
            intervalHours, TimeUnit.HOURS
        )
            .setConstraints(constraints)
            .setInitialDelay(initialDelay, TimeUnit.MILLISECONDS)
            .addTag(NotificationWorker.WORK_TAG)
            .setBackoffCriteria(
                BackoffPolicy.LINEAR,
                WorkRequest.MIN_BACKOFF_MILLIS,
                TimeUnit.MILLISECONDS
            )
            .build()

        // 启动工作
        workManager.enqueueUniquePeriodicWork(
            NotificationWorker.WORK_NAME,
            ExistingPeriodicWorkPolicy.REPLACE,
            notificationWork
        )
    }

    /**
     * 立即执行一次通知检查
     */
    fun triggerImmediateNotification() {
        val constraints = Constraints.Builder()
            .setRequiredNetworkType(NetworkType.NOT_REQUIRED)
            .build()

        val immediateWork = OneTimeWorkRequestBuilder<NotificationWorker>()
            .setConstraints(constraints)
            .addTag(NotificationWorker.WORK_TAG)
            .build()

        workManager.enqueueUniqueWork(
            "immediate_notification",
            ExistingWorkPolicy.REPLACE,
            immediateWork
        )
    }

    /**
     * 取消定期通知
     */
    fun cancelPeriodicNotifications() {
        workManager.cancelUniqueWork(NotificationWorker.WORK_NAME)
        workManager.cancelAllWorkByTag(NotificationWorker.WORK_TAG)
    }

    /**
     * 检查通知工作状态
     */
    fun getNotificationWorkStatus(): androidx.lifecycle.LiveData<List<WorkInfo>> {
        return workManager.getWorkInfosByTagLiveData(NotificationWorker.WORK_TAG)
    }

    /**
     * 更新通知设置
     */
    fun updateNotificationSettings(
        enabled: Boolean,
        intervalHours: Long = DEFAULT_REPEAT_INTERVAL_HOURS,
        notificationHour: Int = DEFAULT_NOTIFICATION_HOUR,
        notificationMinute: Int = DEFAULT_NOTIFICATION_MINUTE
    ) {
        if (enabled) {
            startPeriodicNotifications(intervalHours, notificationHour, notificationMinute)
        } else {
            cancelPeriodicNotifications()
        }
    }

    /**
     * 计算到下次通知时间的初始延迟
     */
    private fun calculateInitialDelay(hour: Int, minute: Int): Long {
        val now = System.currentTimeMillis()
        val calendar = java.util.Calendar.getInstance().apply {
            timeInMillis = now
            set(java.util.Calendar.HOUR_OF_DAY, hour)
            set(java.util.Calendar.MINUTE, minute)
            set(java.util.Calendar.SECOND, 0)
            set(java.util.Calendar.MILLISECOND, 0)
        }

        // 如果今天的时间已经过了，设置为明天
        if (calendar.timeInMillis <= now) {
            calendar.add(java.util.Calendar.DAY_OF_MONTH, 1)
        }

        return calendar.timeInMillis - now
    }

    /**
     * 检查是否有正在运行的通知工作
     */
    suspend fun isNotificationWorkRunning(): Boolean {
        val workInfos = workManager.getWorkInfosByTag(NotificationWorker.WORK_TAG).get()
        return workInfos.any { workInfo ->
            workInfo.state == WorkInfo.State.RUNNING || workInfo.state == WorkInfo.State.ENQUEUED
        }
    }

    /**
     * 获取下次通知时间
     */
    suspend fun getNextNotificationTime(): Long? {
        val workInfos = workManager.getWorkInfosByTag(NotificationWorker.WORK_TAG).get()
        return workInfos.firstOrNull { it.state == WorkInfo.State.ENQUEUED }?.nextScheduleTimeMillis
    }

    /**
     * 清除所有通知
     */
    fun clearAllNotifications() {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
        notificationManager.cancelAll()
    }

    /**
     * 检查通知权限
     */
    fun hasNotificationPermission(): Boolean {
        val notificationManager = context.getSystemService(Context.NOTIFICATION_SERVICE) as android.app.NotificationManager
        return if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.N) {
            notificationManager.areNotificationsEnabled()
        } else {
            true
        }
    }

    /**
     * 请求通知权限（需要在Activity中调用）
     */
    fun requestNotificationPermission(activity: androidx.fragment.app.FragmentActivity) {
        if (android.os.Build.VERSION.SDK_INT >= android.os.Build.VERSION_CODES.TIRAMISU) {
            if (androidx.core.content.ContextCompat.checkSelfPermission(
                    activity,
                    android.Manifest.permission.POST_NOTIFICATIONS
                ) != android.content.pm.PackageManager.PERMISSION_GRANTED
            ) {
                androidx.core.app.ActivityCompat.requestPermissions(
                    activity,
                    arrayOf(android.Manifest.permission.POST_NOTIFICATIONS),
                    1001
                )
            }
        }
    }
}
