package com.example.dataassistant.ui.fragment

import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import androidx.appcompat.widget.SearchView
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import androidx.lifecycle.LiveData
import androidx.navigation.fragment.findNavController
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.dataassistant.R
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.databinding.FragmentProductListBinding
import com.example.dataassistant.ui.adapter.ProductAdapter
import com.example.dataassistant.ui.viewmodel.ProductViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint

/**
 * 商品列表Fragment
 */
@AndroidEntryPoint
class ProductListFragment : Fragment() {

    private var _binding: FragmentProductListBinding? = null
    private val binding get() = _binding!!

    private val viewModel: ProductViewModel by viewModels()
    private lateinit var productAdapter: ProductAdapter

    private var currentFilter = FilterType.ALL
    private var currentSort = SortType.CREATE_TIME_DESC
    private var searchQuery = ""
    private var currentProductsLiveData: LiveData<List<Product>>? = null

    enum class FilterType {
        ALL, NORMAL, WARNING, EXPIRED, EXPIRING_SOON
    }

    enum class SortType {
        NAME_ASC, NAME_DESC,
        CREATE_TIME_ASC, CREATE_TIME_DESC,
        EXPIRY_DATE_ASC, EXPIRY_DATE_DESC
    }

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentProductListBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        setupMenu()
        setupRecyclerView()
        setupObservers()
        setupClickListeners()
        setupSearch()

        // 加载统计信息
        viewModel.loadStatistics()

        // 默认显示所有商品
        loadProducts()
    }

    private fun setupMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_product_list, menu)

                // 设置搜索功能
                val searchItem = menu.findItem(R.id.action_search)
                val searchView = searchItem.actionView as SearchView
                searchView.setOnQueryTextListener(object : SearchView.OnQueryTextListener {
                    override fun onQueryTextSubmit(query: String?): Boolean {
                        return false
                    }

                    override fun onQueryTextChange(newText: String?): Boolean {
                        searchQuery = newText ?: ""
                        loadProducts()
                        return true
                    }
                })
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_sort -> {
                        showSortDialog()
                        true
                    }
                    R.id.action_delete_all -> {
                        showDeleteAllDialog()
                        true
                    }
                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    private fun setupRecyclerView() {
        productAdapter = ProductAdapter(
            onItemClick = { product ->
                viewModel.selectProduct(product)
                // 导航到商品详情
                val action = ProductListFragmentDirections.actionProductListToProductDetail(product.id)
                findNavController().navigate(action)
            },
            onItemLongClick = { product ->
                showProductOptionsDialog(product)
                true
            }
        )

        binding.recyclerViewProducts.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = productAdapter
        }
    }

    private fun setupSearch() {
        // 搜索功能已在setupMenu中实现
    }

    private fun setupObservers() {
        // 观察UI状态
        viewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            binding.progressBar.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE

            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }

            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                viewModel.clearMessage()
            }

            uiState.statistics?.let { stats ->
                updateStatistics(stats)
            }
        }
    }

    private fun setupClickListeners() {
        // 筛选按钮
        binding.chipAll.setOnClickListener {
            currentFilter = FilterType.ALL
            updateFilterChips()
            loadProducts()
        }

        binding.chipNormal.setOnClickListener {
            currentFilter = FilterType.NORMAL
            updateFilterChips()
            loadProducts()
        }

        binding.chipWarning.setOnClickListener {
            currentFilter = FilterType.WARNING
            updateFilterChips()
            loadProducts()
        }

        binding.chipExpired.setOnClickListener {
            currentFilter = FilterType.EXPIRED
            updateFilterChips()
            loadProducts()
        }

        binding.chipExpiringSoon.setOnClickListener {
            currentFilter = FilterType.EXPIRING_SOON
            updateFilterChips()
            loadProducts()
        }

        // 刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            loadProducts()
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    private fun loadProducts() {
        // 移除之前的观察者
        currentProductsLiveData?.removeObservers(viewLifecycleOwner)

        // 根据筛选条件获取对应的LiveData
        currentProductsLiveData = when (currentFilter) {
            FilterType.ALL -> viewModel.allProducts
            FilterType.NORMAL -> viewModel.normalProducts
            FilterType.WARNING -> viewModel.warningProducts
            FilterType.EXPIRED -> viewModel.expiredProducts
            FilterType.EXPIRING_SOON -> viewModel.getExpiringProducts(7)
        }

        // 观察新的LiveData
        currentProductsLiveData?.observe(viewLifecycleOwner) { products ->
            val filteredProducts = if (searchQuery.isNotEmpty()) {
                products.filter { product ->
                    product.name.contains(searchQuery, ignoreCase = true) ||
                    product.brand?.contains(searchQuery, ignoreCase = true) == true ||
                    product.category?.contains(searchQuery, ignoreCase = true) == true
                }
            } else {
                products
            }

            val sortedProducts = sortProducts(filteredProducts)
            productAdapter.submitList(sortedProducts)
            updateEmptyState(sortedProducts.isEmpty())
        }
    }

    private fun sortProducts(products: List<Product>): List<Product> {
        return when (currentSort) {
            SortType.NAME_ASC -> products.sortedBy { it.name }
            SortType.NAME_DESC -> products.sortedByDescending { it.name }
            SortType.CREATE_TIME_ASC -> products.sortedBy { it.createTime }
            SortType.CREATE_TIME_DESC -> products.sortedByDescending { it.createTime }
            SortType.EXPIRY_DATE_ASC -> products.sortedBy { it.expiryDate }
            SortType.EXPIRY_DATE_DESC -> products.sortedByDescending { it.expiryDate }
        }
    }

    private fun updateFilterChips() {
        binding.chipAll.isChecked = currentFilter == FilterType.ALL
        binding.chipNormal.isChecked = currentFilter == FilterType.NORMAL
        binding.chipWarning.isChecked = currentFilter == FilterType.WARNING
        binding.chipExpired.isChecked = currentFilter == FilterType.EXPIRED
        binding.chipExpiringSoon.isChecked = currentFilter == FilterType.EXPIRING_SOON
    }

    private fun showSortDialog() {
        val sortOptions = arrayOf(
            "按名称升序", "按名称降序",
            "按创建时间升序", "按创建时间降序",
            "按到期时间升序", "按到期时间降序"
        )

        val currentIndex = when (currentSort) {
            SortType.NAME_ASC -> 0
            SortType.NAME_DESC -> 1
            SortType.CREATE_TIME_ASC -> 2
            SortType.CREATE_TIME_DESC -> 3
            SortType.EXPIRY_DATE_ASC -> 4
            SortType.EXPIRY_DATE_DESC -> 5
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("排序方式")
            .setSingleChoiceItems(sortOptions, currentIndex) { dialog, which ->
                currentSort = when (which) {
                    0 -> SortType.NAME_ASC
                    1 -> SortType.NAME_DESC
                    2 -> SortType.CREATE_TIME_ASC
                    3 -> SortType.CREATE_TIME_DESC
                    4 -> SortType.EXPIRY_DATE_ASC
                    5 -> SortType.EXPIRY_DATE_DESC
                    else -> SortType.CREATE_TIME_DESC
                }
                loadProducts()
                dialog.dismiss()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showDeleteAllDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除所有商品")
            .setMessage("确定要删除所有商品吗？此操作不可撤销。")
            .setPositiveButton("删除") { _, _ ->
                viewModel.deleteAllProducts()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showProductOptionsDialog(product: Product) {
        val options = arrayOf("查看详情", "编辑", "删除")

        MaterialAlertDialogBuilder(requireContext())
            .setTitle(product.name)
            .setItems(options) { _, which ->
                when (which) {
                    0 -> {
                        // 查看详情
                        val action = ProductListFragmentDirections.actionProductListToProductDetail(product.id)
                        findNavController().navigate(action)
                    }
                    1 -> {
                        // 编辑
                        val action = ProductListFragmentDirections.actionProductListToProductEdit(product.id)
                        findNavController().navigate(action)
                    }
                    2 -> {
                        // 删除
                        showDeleteProductDialog(product)
                    }
                }
            }
            .show()
    }

    private fun showDeleteProductDialog(product: Product) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除商品")
            .setMessage("确定要删除「${product.name}」吗？")
            .setPositiveButton("删除") { _, _ ->
                viewModel.deleteProduct(product)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        binding.textViewEmpty.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.recyclerViewProducts.visibility = if (isEmpty) View.GONE else View.VISIBLE

        // 更新空状态文本
        val emptyText = when (currentFilter) {
            FilterType.ALL -> if (searchQuery.isNotEmpty()) "没有找到匹配的商品" else "暂无商品数据"
            FilterType.NORMAL -> "没有正常状态的商品"
            FilterType.WARNING -> "没有临期商品"
            FilterType.EXPIRED -> "没有过期商品"
            FilterType.EXPIRING_SOON -> "没有即将过期的商品"
        }
        binding.textViewEmpty.text = emptyText
    }

    private fun updateStatistics(statistics: com.example.dataassistant.ui.viewmodel.ProductStatistics) {
        binding.textViewStatistics.text = "总计: ${statistics.totalCount} | " +
                "正常: ${statistics.normalCount} | " +
                "临期: ${statistics.warningCount} | " +
                "过期: ${statistics.expiredCount}"
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
