package com.example.dataassistant.ui.fragment

import android.content.SharedPreferences
import android.os.Bundle
import android.view.LayoutInflater
import android.view.Menu
import android.view.MenuInflater
import android.view.MenuItem
import android.view.View
import android.view.ViewGroup
import android.widget.ArrayAdapter
import androidx.core.view.MenuHost
import androidx.core.view.MenuProvider
import androidx.fragment.app.Fragment
import androidx.fragment.app.viewModels
import androidx.lifecycle.Lifecycle
import android.preference.PreferenceManager
import androidx.recyclerview.widget.LinearLayoutManager
import com.example.dataassistant.R
import com.example.dataassistant.data.entity.ShelfLifeConfig
import com.example.dataassistant.databinding.FragmentConfigBinding
import com.example.dataassistant.ui.adapter.ConfigAdapter
import com.example.dataassistant.ui.dialog.ConfigEditDialog
import com.example.dataassistant.ui.viewmodel.ConfigViewModel
import com.google.android.material.dialog.MaterialAlertDialogBuilder
import com.google.android.material.snackbar.Snackbar
import dagger.hilt.android.AndroidEntryPoint

/**
 * 配置管理Fragment
 */
@AndroidEntryPoint
class ConfigFragment : Fragment() {

    private var _binding: FragmentConfigBinding? = null
    private val binding get() = _binding!!

    private val viewModel: ConfigViewModel by viewModels()
    private lateinit var configAdapter: ConfigAdapter
    private lateinit var sharedPreferences: SharedPreferences

    override fun onCreateView(
        inflater: LayoutInflater,
        container: ViewGroup?,
        savedInstanceState: Bundle?
    ): View {
        _binding = FragmentConfigBinding.inflate(inflater, container, false)
        return binding.root
    }

    override fun onViewCreated(view: View, savedInstanceState: Bundle?) {
        super.onViewCreated(view, savedInstanceState)

        sharedPreferences = PreferenceManager.getDefaultSharedPreferences(requireContext())

        setupMenu()
        setupRecyclerView()
        setupObservers()
        setupClickListeners()
        setupGeneralSettings()

        // 加载配置列表
        viewModel.loadConfigs()
    }

    private fun setupMenu() {
        val menuHost: MenuHost = requireActivity()
        menuHost.addMenuProvider(object : MenuProvider {
            override fun onCreateMenu(menu: Menu, menuInflater: MenuInflater) {
                menuInflater.inflate(R.menu.menu_config, menu)
            }

            override fun onMenuItemSelected(menuItem: MenuItem): Boolean {
                return when (menuItem.itemId) {
                    R.id.action_add_config -> {
                        showAddConfigDialog()
                        true
                    }
                    R.id.action_reset_configs -> {
                        showResetConfigsDialog()
                        true
                    }
                    else -> false
                }
            }
        }, viewLifecycleOwner, Lifecycle.State.RESUMED)
    }

    private fun setupRecyclerView() {
        configAdapter = ConfigAdapter(
            onItemClick = { config ->
                showConfigDetailsDialog(config)
            },
            onEditClick = { config ->
                showEditConfigDialog(config)
            },
            onDeleteClick = { config ->
                showDeleteConfigDialog(config)
            },
            onDefaultClick = { config ->
                viewModel.setDefaultConfig(config.id)
            },
            onToggleEnabled = { config, enabled ->
                viewModel.updateConfigEnabled(config.id, enabled)
            }
        )

        binding.recyclerViewConfigs.apply {
            layoutManager = LinearLayoutManager(requireContext())
            adapter = configAdapter
        }
    }

    private fun setupObservers() {
        // 观察配置列表
        viewModel.allConfigs.observe(viewLifecycleOwner) { configs ->
            configAdapter.submitList(configs)
            updateEmptyState(configs.isEmpty())
        }

        // 观察UI状态
        viewModel.uiState.observe(viewLifecycleOwner) { uiState ->
            binding.progressBar.visibility = if (uiState.isLoading) View.VISIBLE else View.GONE

            uiState.error?.let { error ->
                Snackbar.make(binding.root, error, Snackbar.LENGTH_LONG).show()
                viewModel.clearError()
            }

            uiState.message?.let { message ->
                Snackbar.make(binding.root, message, Snackbar.LENGTH_SHORT).show()
                viewModel.clearMessage()
            }
        }
    }

    private fun setupClickListeners() {
        // 添加配置按钮
        binding.buttonAddConfig.setOnClickListener {
            showAddConfigDialog()
        }

        binding.buttonAddFirstConfig.setOnClickListener {
            showAddConfigDialog()
        }

        // 数据管理按钮
        binding.buttonExportData.setOnClickListener {
            exportData()
        }

        binding.buttonImportData.setOnClickListener {
            importData()
        }

        binding.buttonClearData.setOnClickListener {
            showClearDataDialog()
        }

        // 刷新
        binding.swipeRefreshLayout.setOnRefreshListener {
            viewModel.loadConfigs()
            binding.swipeRefreshLayout.isRefreshing = false
        }
    }

    private fun setupGeneralSettings() {
        // 设置通知频率选择器
        val frequencies = arrayOf("每小时", "每6小时", "每12小时", "每天", "每周")
        val adapter = ArrayAdapter(requireContext(), android.R.layout.simple_spinner_item, frequencies)
        adapter.setDropDownViewResource(android.R.layout.simple_spinner_dropdown_item)
        binding.spinnerNotificationFrequency.adapter = adapter

        // 加载保存的设置
        loadGeneralSettings()

        // 设置监听器
        binding.editTextDefaultWarningDays.setOnFocusChangeListener { _, hasFocus ->
            if (!hasFocus) {
                saveGeneralSettings()
            }
        }

        binding.spinnerNotificationFrequency.onItemSelectedListener = object : android.widget.AdapterView.OnItemSelectedListener {
            override fun onItemSelected(parent: android.widget.AdapterView<*>?, view: View?, position: Int, id: Long) {
                saveGeneralSettings()
            }
            override fun onNothingSelected(parent: android.widget.AdapterView<*>?) {}
        }

        binding.switchEnableNotifications.setOnCheckedChangeListener { _, _ ->
            saveGeneralSettings()
        }
    }

    private fun loadGeneralSettings() {
        val defaultWarningDays = sharedPreferences.getInt("default_warning_days", 7)
        val notificationFrequency = sharedPreferences.getInt("notification_frequency", 2) // 默认每12小时
        val enableNotifications = sharedPreferences.getBoolean("enable_notifications", true)

        binding.editTextDefaultWarningDays.setText(defaultWarningDays.toString())
        binding.spinnerNotificationFrequency.setSelection(notificationFrequency)
        binding.switchEnableNotifications.isChecked = enableNotifications
    }

    private fun saveGeneralSettings() {
        val warningDaysText = binding.editTextDefaultWarningDays.text.toString()
        val warningDays = warningDaysText.toIntOrNull() ?: 7
        val notificationFrequency = binding.spinnerNotificationFrequency.selectedItemPosition
        val enableNotifications = binding.switchEnableNotifications.isChecked

        sharedPreferences.edit()
            .putInt("default_warning_days", warningDays)
            .putInt("notification_frequency", notificationFrequency)
            .putBoolean("enable_notifications", enableNotifications)
            .apply()
    }

    private fun exportData() {
        // TODO: 实现数据导出功能
        Snackbar.make(binding.root, "数据导出功能开发中", Snackbar.LENGTH_SHORT).show()
    }

    private fun importData() {
        // TODO: 实现数据导入功能
        Snackbar.make(binding.root, "数据导入功能开发中", Snackbar.LENGTH_SHORT).show()
    }

    private fun showClearDataDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("清空所有数据")
            .setMessage("确定要清空所有商品和配置数据吗？此操作不可撤销。")
            .setPositiveButton("清空") { _, _ ->
                clearAllData()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun clearAllData() {
        // TODO: 实现清空所有数据功能
        Snackbar.make(binding.root, "数据清空功能开发中", Snackbar.LENGTH_SHORT).show()
    }

    private fun showAddConfigDialog() {
        val dialog = ConfigEditDialog.newInstance()
        dialog.setOnConfigSavedListener { config ->
            viewModel.addConfig(config)
        }
        dialog.show(parentFragmentManager, "ADD_CONFIG_DIALOG")
    }

    private fun showEditConfigDialog(config: ShelfLifeConfig) {
        val dialog = ConfigEditDialog.newInstance(config)
        dialog.setOnConfigSavedListener { updatedConfig ->
            viewModel.updateConfig(updatedConfig)
        }
        dialog.show(parentFragmentManager, "EDIT_CONFIG_DIALOG")
    }

    private fun showConfigDetailsDialog(config: ShelfLifeConfig) {
        val details = buildString {
            appendLine("配置名称: ${config.name}")
            appendLine("描述: ${config.description ?: "无"}")
            appendLine("保质期范围: ${config.minShelfLifeDays}-${config.maxShelfLifeDays}天")
            appendLine("临期提醒: ${config.warningDays}天")
            appendLine("下架提醒: ${config.removalDays}天")
            appendLine("折扣提醒: ${config.discountDays}天")
            appendLine("通知频率: 每${config.notificationFrequency}小时")
            appendLine("通知时间: ${config.notificationTime}")
            appendLine("状态: ${if (config.isEnabled) "启用" else "禁用"}")
            appendLine("默认配置: ${if (config.isDefault) "是" else "否"}")
        }

        MaterialAlertDialogBuilder(requireContext())
            .setTitle("配置详情")
            .setMessage(details)
            .setPositiveButton("编辑") { _, _ ->
                showEditConfigDialog(config)
            }
            .setNegativeButton("关闭", null)
            .show()
    }

    private fun showDeleteConfigDialog(config: ShelfLifeConfig) {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("删除配置")
            .setMessage("确定要删除配置「${config.name}」吗？")
            .setPositiveButton("删除") { _, _ ->
                viewModel.deleteConfig(config)
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun showResetConfigsDialog() {
        MaterialAlertDialogBuilder(requireContext())
            .setTitle("重置配置")
            .setMessage("确定要重置所有配置为默认设置吗？此操作不可撤销。")
            .setPositiveButton("重置") { _, _ ->
                viewModel.resetToDefaultConfigs()
            }
            .setNegativeButton("取消", null)
            .show()
    }

    private fun updateEmptyState(isEmpty: Boolean) {
        binding.textViewEmpty.visibility = if (isEmpty) View.VISIBLE else View.GONE
        binding.recyclerViewConfigs.visibility = if (isEmpty) View.GONE else View.VISIBLE
    }

    override fun onDestroyView() {
        super.onDestroyView()
        _binding = null
    }
}
