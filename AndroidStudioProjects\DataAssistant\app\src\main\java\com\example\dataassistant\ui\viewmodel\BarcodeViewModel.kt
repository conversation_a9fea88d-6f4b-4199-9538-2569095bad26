package com.example.dataassistant.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.dataassistant.data.entity.BarcodeProduct
import com.example.dataassistant.data.entity.BarcodeQueryResult
import com.example.dataassistant.data.entity.ProductSuggestion
import com.example.dataassistant.data.repository.BarcodeProductRepository
import com.example.dataassistant.utils.ValidationUtils
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 条码扫描ViewModel
 */
@HiltViewModel
class BarcodeViewModel @Inject constructor(
    private val barcodeProductRepository: BarcodeProductRepository
) : ViewModel() {

    private val _uiState = MutableLiveData<BarcodeUiState>()
    val uiState: LiveData<BarcodeUiState> = _uiState

    private val _queryResult = MutableLiveData<BarcodeQueryResult?>()
    val queryResult: LiveData<BarcodeQueryResult?> = _queryResult

    private val _searchResults = MutableLiveData<List<BarcodeProduct>>()
    val searchResults: LiveData<List<BarcodeProduct>> = _searchResults

    private val _suggestions = MutableLiveData<List<ProductSuggestion>>()
    val suggestions: LiveData<List<ProductSuggestion>> = _suggestions

    init {
        _uiState.value = BarcodeUiState()
    }

    /**
     * 扫描条码查询商品
     */
    fun scanBarcode(barcode: String) {
        // 验证条码格式
        val validationResult = ValidationUtils.validateBarcode(barcode)
        if (!validationResult.isValid) {
            _uiState.value = _uiState.value?.copy(error = validationResult.getFirstError())
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isScanning = true, error = null)
                val result = barcodeProductRepository.getProductByBarcode(barcode)
                _queryResult.value = result
                _uiState.value = _uiState.value?.copy(
                    isScanning = false,
                    lastScannedBarcode = barcode
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isScanning = false,
                    error = "扫描失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 搜索商品
     */
    fun searchProducts(keyword: String, limit: Int = 20) {
        // 验证搜索关键词
        val validationResult = ValidationUtils.validateSearchKeyword(keyword)
        if (!validationResult.isValid) {
            _searchResults.value = emptyList()
            _uiState.value = _uiState.value?.copy(error = validationResult.getFirstError())
            return
        }

        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isSearching = true)
                val results = barcodeProductRepository.searchProducts(keyword, limit)
                _searchResults.value = results
                _uiState.value = _uiState.value?.copy(isSearching = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isSearching = false,
                    error = "搜索失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取搜索建议
     */
    fun getSearchSuggestions(keyword: String, limit: Int = 10) {
        if (keyword.isBlank()) {
            _suggestions.value = emptyList()
            return
        }

        viewModelScope.launch {
            try {
                val suggestions = barcodeProductRepository.getSearchSuggestions(keyword, limit)
                _suggestions.value = suggestions
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "获取建议失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 添加条码商品
     */
    fun addBarcodeProduct(product: BarcodeProduct) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                barcodeProductRepository.insertProduct(product)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "商品信息添加成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "添加失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 更新条码商品
     */
    fun updateBarcodeProduct(product: BarcodeProduct) {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                barcodeProductRepository.updateProduct(product)
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    message = "商品信息更新成功"
                )
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "更新失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取最近使用的商品
     */
    fun loadRecentlyUsedProducts(limit: Int = 10) {
        viewModelScope.launch {
            try {
                val products = barcodeProductRepository.getRecentlyUsedProducts(limit)
                _uiState.value = _uiState.value?.copy(recentProducts = products)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "加载最近使用商品失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 获取高可信度商品
     */
    fun loadHighConfidenceProducts(minConfidence: Float = 0.8f) {
        viewModelScope.launch {
            try {
                val products = barcodeProductRepository.getHighConfidenceProducts(minConfidence)
                _uiState.value = _uiState.value?.copy(highConfidenceProducts = products)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    error = "加载高可信度商品失败: ${e.message}"
                )
            }
        }
    }

    /**
     * 清除查询结果
     */
    fun clearQueryResult() {
        _queryResult.value = null
    }

    /**
     * 清除搜索结果
     */
    fun clearSearchResults() {
        _searchResults.value = emptyList()
    }

    /**
     * 清除建议
     */
    fun clearSuggestions() {
        _suggestions.value = emptyList()
    }

    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value?.copy(error = null)
    }

    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value?.copy(message = null)
    }
}

/**
 * 条码扫描UI状态
 */
data class BarcodeUiState(
    val isScanning: Boolean = false,
    val isSearching: Boolean = false,
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null,
    val lastScannedBarcode: String? = null,
    val recentProducts: List<BarcodeProduct> = emptyList(),
    val highConfidenceProducts: List<BarcodeProduct> = emptyList()
)
