package com.example.dataassistant.data.dao

import androidx.room.*
import com.example.dataassistant.data.entity.BarcodeProduct

/**
 * 源统计结果数据类
 */
data class SourceCount(
    val source: String,
    val count: Int
)

/**
 * 条码商品数据访问对象
 */
@Dao
interface BarcodeProductDao {

    /**
     * 根据条码查询商品
     */
    @Query("SELECT * FROM barcode_products WHERE barcode = :barcode")
    suspend fun getProductByBarcode(barcode: String): BarcodeProduct?

    /**
     * 搜索商品（按名称或品牌）
     */
    @Query("""
        SELECT * FROM barcode_products
        WHERE name LIKE '%' || :keyword || '%'
        OR brand LIKE '%' || :keyword || '%'
        OR fullName LIKE '%' || :keyword || '%'
        ORDER BY
            CASE
                WHEN name LIKE :keyword || '%' THEN 1
                WHEN brand LIKE :keyword || '%' THEN 2
                WHEN name LIKE '%' || :keyword || '%' THEN 3
                ELSE 4
            END,
            confidence DESC,
            lastUsedTime DESC
        LIMIT :limit
    """)
    suspend fun searchProducts(keyword: String, limit: Int = 20): List<BarcodeProduct>

    /**
     * 获取搜索建议
     */
    @Query("""
        SELECT DISTINCT *
        FROM barcode_products
        WHERE name LIKE :keyword || '%'
        OR brand LIKE :keyword || '%'
        ORDER BY
            CASE
                WHEN name LIKE :keyword || '%' THEN 1
                WHEN brand LIKE :keyword || '%' THEN 2
                ELSE 3
            END,
            confidence DESC
        LIMIT :limit
    """)
    suspend fun getSearchSuggestions(keyword: String, limit: Int = 10): List<BarcodeProduct>

    /**
     * 插入商品
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: BarcodeProduct)

    /**
     * 批量插入商品
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<BarcodeProduct>)

    /**
     * 更新商品
     */
    @Update
    suspend fun updateProduct(product: BarcodeProduct)

    /**
     * 删除商品
     */
    @Delete
    suspend fun deleteProduct(product: BarcodeProduct)

    /**
     * 根据条码删除商品
     */
    @Query("DELETE FROM barcode_products WHERE barcode = :barcode")
    suspend fun deleteProductByBarcode(barcode: String)

    /**
     * 更新最后使用时间
     */
    @Query("UPDATE barcode_products SET lastUsedTime = :time WHERE barcode = :barcode")
    suspend fun updateLastUsedTime(barcode: String, time: Long)

    /**
     * 获取所有商品
     */
    @Query("SELECT * FROM barcode_products ORDER BY lastUsedTime DESC, confidence DESC")
    suspend fun getAllProducts(): List<BarcodeProduct>

    /**
     * 获取按来源分组的商品数量
     */
    @Query("SELECT source, COUNT(*) as count FROM barcode_products GROUP BY source")
    suspend fun getProductCountBySource(): List<SourceCount>

    /**
     * 获取最近使用的商品
     */
    @Query("""
        SELECT * FROM barcode_products
        WHERE lastUsedTime IS NOT NULL
        ORDER BY lastUsedTime DESC
        LIMIT :limit
    """)
    suspend fun getRecentlyUsedProducts(limit: Int = 10): List<BarcodeProduct>

    /**
     * 获取高可信度商品
     */
    @Query("""
        SELECT * FROM barcode_products
        WHERE confidence >= :minConfidence
        ORDER BY confidence DESC, lastUsedTime DESC
    """)
    suspend fun getHighConfidenceProducts(minConfidence: Float = 0.8f): List<BarcodeProduct>

    /**
     * 清空所有商品
     */
    @Query("DELETE FROM barcode_products")
    suspend fun deleteAllProducts()
}
