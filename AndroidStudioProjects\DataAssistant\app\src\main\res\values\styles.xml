<?xml version="1.0" encoding="utf-8"?>
<resources>
    
    <!-- 卡片样式 -->
    <style name="AppCardView" parent="Widget.Material3.CardView.Elevated">
        <item name="cardCornerRadius">16dp</item>
        <item name="cardElevation">2dp</item>
        <item name="cardBackgroundColor">@color/card_background</item>
        <item name="android:layout_margin">8dp</item>
    </style>

    <style name="AppCardView.Compact">
        <item name="cardCornerRadius">12dp</item>
        <item name="cardElevation">1dp</item>
        <item name="android:layout_margin">4dp</item>
    </style>

    <!-- 文本样式 -->
    <style name="AppTextAppearance" parent="TextAppearance.Material3.BodyLarge">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="AppTextAppearance.Headline" parent="TextAppearance.Material3.HeadlineSmall">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="AppTextAppearance.Title" parent="TextAppearance.Material3.TitleMedium">
        <item name="android:textColor">@color/text_primary</item>
        <item name="android:textStyle">bold</item>
    </style>

    <style name="AppTextAppearance.Body" parent="TextAppearance.Material3.BodyMedium">
        <item name="android:textColor">@color/text_primary</item>
    </style>

    <style name="AppTextAppearance.Caption" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_secondary</item>
    </style>

    <style name="AppTextAppearance.Hint" parent="TextAppearance.Material3.BodySmall">
        <item name="android:textColor">@color/text_hint</item>
    </style>

    <!-- 按钮样式 -->
    <style name="AppButton" parent="Widget.Material3.Button">
        <item name="android:textColor">@color/white</item>
        <item name="backgroundTint">@color/primary</item>
        <item name="cornerRadius">12dp</item>
    </style>

    <style name="AppButton.Outlined" parent="Widget.Material3.Button.OutlinedButton">
        <item name="android:textColor">@color/primary</item>
        <item name="strokeColor">@color/primary</item>
        <item name="cornerRadius">12dp</item>
    </style>

    <style name="AppButton.Text" parent="Widget.Material3.Button.TextButton">
        <item name="android:textColor">@color/primary</item>
    </style>

    <style name="AppButton.Icon" parent="Widget.Material3.Button.IconButton">
        <item name="iconTint">@color/primary</item>
    </style>

    <!-- 状态标签样式 -->
    <style name="StatusTag">
        <item name="android:layout_width">wrap_content</item>
        <item name="android:layout_height">wrap_content</item>
        <item name="android:paddingStart">8dp</item>
        <item name="android:paddingEnd">8dp</item>
        <item name="android:paddingTop">4dp</item>
        <item name="android:paddingBottom">4dp</item>
        <item name="android:textSize">12sp</item>
        <item name="android:textStyle">bold</item>
        <item name="android:textColor">@color/white</item>
    </style>

    <style name="StatusTag.Normal">
        <item name="android:background">@drawable/bg_status_normal</item>
    </style>

    <style name="StatusTag.Warning">
        <item name="android:background">@drawable/bg_status_warning</item>
    </style>

    <style name="StatusTag.Expired">
        <item name="android:background">@drawable/bg_status_expired</item>
    </style>

    <style name="StatusTag.Removal">
        <item name="android:background">@drawable/bg_status_removal</item>
    </style>

    <!-- 输入框样式 -->
    <style name="AppTextInputLayout" parent="Widget.Material3.TextInputLayout.OutlinedBox">
        <item name="boxCornerRadiusTopStart">12dp</item>
        <item name="boxCornerRadiusTopEnd">12dp</item>
        <item name="boxCornerRadiusBottomStart">12dp</item>
        <item name="boxCornerRadiusBottomEnd">12dp</item>
        <item name="boxStrokeColor">@color/primary</item>
        <item name="hintTextColor">@color/text_secondary</item>
    </style>

    <!-- 分割线样式 -->
    <style name="AppDivider">
        <item name="android:layout_width">match_parent</item>
        <item name="android:layout_height">1dp</item>
        <item name="android:background">@color/divider</item>
    </style>

</resources>
