package com.example.dataassistant.worker

import android.app.NotificationChannel
import android.app.NotificationManager
import android.app.PendingIntent
import android.content.Context
import android.content.Intent
import android.os.Build
import androidx.core.app.NotificationCompat
import androidx.work.CoroutineWorker
import androidx.work.WorkerParameters
import com.example.dataassistant.MainActivity
import com.example.dataassistant.R
import com.example.dataassistant.data.database.AppDatabase
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.utils.ProductStatusCalculator
import com.example.dataassistant.utils.ProductStatusInfo
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.withContext

/**
 * 通知提醒工作类
 */
class NotificationWorker(
    context: Context,
    params: WorkerParameters
) : CoroutineWorker(context, params) {

    companion object {
        const val CHANNEL_ID = "product_expiry_channel"
        const val CHANNEL_NAME = "商品过期提醒"
        const val NOTIFICATION_ID_BASE = 1000

        // 工作标签
        const val WORK_TAG = "product_notification"
        const val WORK_NAME = "product_notification_work"
    }

    override suspend fun doWork(): Result = withContext(Dispatchers.IO) {
        try {
            // 创建通知渠道
            createNotificationChannel()

            // 获取数据库实例
            val database = AppDatabase.getDatabase(applicationContext)
            val productDao = database.productDao()
            val configDao = database.shelfLifeConfigDao()

            // 获取所有启用通知的商品
            val products = productDao.getProductsWithNotificationEnabled()
            val configsList = configDao.getAllConfigsSync()
            val configs = configsList.associateBy { it.id }

            var notificationCount = 0

            // 检查每个商品的状态
            products.forEach { product ->
                val config = product.configId?.let { configs[it] }
                val statusInfo = ProductStatusCalculator.calculateProductStatus(product, config)

                // 如果商品需要提醒
                if (shouldNotify(statusInfo, product)) {
                    sendNotification(product, statusInfo, notificationCount)
                    notificationCount++
                }
            }

            // 如果有多个通知，发送汇总通知
            if (notificationCount > 1) {
                sendSummaryNotification(notificationCount)
            }

            Result.success()
        } catch (e: Exception) {
            Result.failure()
        }
    }

    private fun createNotificationChannel() {
        if (Build.VERSION.SDK_INT >= Build.VERSION_CODES.O) {
            val channel = NotificationChannel(
                CHANNEL_ID,
                CHANNEL_NAME,
                NotificationManager.IMPORTANCE_DEFAULT
            ).apply {
                description = "商品过期和临期提醒通知"
                enableVibration(true)
                enableLights(true)
            }

            val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
            notificationManager.createNotificationChannel(channel)
        }
    }

    private fun shouldNotify(statusInfo: ProductStatusInfo, product: com.example.dataassistant.data.entity.Product): Boolean {
        return when (statusInfo.status) {
            ProductStatus.EXPIRED -> true
            ProductStatus.WARNING -> true
            ProductStatus.REMOVAL -> true
            ProductStatus.NORMAL -> false
        }
    }

    private fun sendNotification(
        product: com.example.dataassistant.data.entity.Product,
        statusInfo: ProductStatusInfo,
        notificationId: Int
    ) {
        val intent = Intent(applicationContext, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("product_id", product.id)
            putExtra("open_product_detail", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            applicationContext,
            notificationId,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val title = when (statusInfo.status) {
            ProductStatus.EXPIRED -> "商品已过期"
            ProductStatus.WARNING -> "商品即将过期"
            ProductStatus.REMOVAL -> "商品需要下架"
            else -> "商品提醒"
        }

        val message = ProductStatusCalculator.getNotificationMessage(product) ?: "请查看商品详情"

        val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle(title)
            .setContentText(message)
            .setStyle(NotificationCompat.BigTextStyle().bigText(message))
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setColor(getNotificationColor(statusInfo.status))
            .build()

        val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID_BASE + notificationId, notification)
    }

    private fun sendSummaryNotification(count: Int) {
        val intent = Intent(applicationContext, MainActivity::class.java).apply {
            flags = Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK
            putExtra("open_product_list", true)
        }

        val pendingIntent = PendingIntent.getActivity(
            applicationContext,
            0,
            intent,
            PendingIntent.FLAG_UPDATE_CURRENT or PendingIntent.FLAG_IMMUTABLE
        )

        val notification = NotificationCompat.Builder(applicationContext, CHANNEL_ID)
            .setSmallIcon(R.drawable.ic_notification)
            .setContentTitle("商品提醒汇总")
            .setContentText("您有 $count 件商品需要关注")
            .setPriority(NotificationCompat.PRIORITY_DEFAULT)
            .setContentIntent(pendingIntent)
            .setAutoCancel(true)
            .setGroup("product_notifications")
            .setGroupSummary(true)
            .build()

        val notificationManager = applicationContext.getSystemService(Context.NOTIFICATION_SERVICE) as NotificationManager
        notificationManager.notify(NOTIFICATION_ID_BASE, notification)
    }

    private fun getNotificationColor(status: ProductStatus): Int {
        return when (status) {
            ProductStatus.EXPIRED -> android.graphics.Color.RED
            ProductStatus.WARNING -> android.graphics.Color.parseColor("#FF9800")
            ProductStatus.REMOVAL -> android.graphics.Color.parseColor("#E91E63")
            else -> android.graphics.Color.BLUE
        }
    }
}
