package com.example.dataassistant.ui.adapter

import android.view.LayoutInflater
import android.view.ViewGroup
import androidx.recyclerview.widget.DiffUtil
import androidx.recyclerview.widget.ListAdapter
import androidx.recyclerview.widget.RecyclerView
import com.example.dataassistant.data.entity.ShelfLifeConfig
import com.example.dataassistant.databinding.ItemConfigBinding

/**
 * 配置列表适配器
 */
class ConfigAdapter(
    private val onItemClick: (ShelfLifeConfig) -> Unit,
    private val onEditClick: (ShelfLifeConfig) -> Unit,
    private val onDeleteClick: (ShelfLifeConfig) -> Unit,
    private val onDefaultClick: (ShelfLifeConfig) -> Unit,
    private val onToggleEnabled: (ShelfLifeConfig, Boolean) -> Unit
) : ListAdapter<ShelfLifeConfig, ConfigAdapter.ConfigViewHolder>(ConfigDiffCallback()) {

    override fun onCreateViewHolder(parent: ViewGroup, viewType: Int): ConfigViewHolder {
        val binding = ItemConfigBinding.inflate(
            LayoutInflater.from(parent.context),
            parent,
            false
        )
        return ConfigViewHolder(binding)
    }

    override fun onBindViewHolder(holder: ConfigViewHolder, position: Int) {
        holder.bind(getItem(position))
    }

    inner class ConfigViewHolder(
        private val binding: ItemConfigBinding
    ) : RecyclerView.ViewHolder(binding.root) {

        fun bind(config: ShelfLifeConfig) {
            binding.apply {
                // 基本信息
                textViewConfigName.text = config.name
                textViewConfigDescription.text = config.description ?: "无描述"
                
                // 保质期范围
                textViewShelfLifeRange.text = "${config.minShelfLifeDays}-${config.maxShelfLifeDays}天"
                
                // 提醒设置
                textViewWarningDays.text = "临期: ${config.warningDays}天"
                textViewRemovalDays.text = "下架: ${config.removalDays}天"
                textViewDiscountDays.text = "折扣: ${config.discountDays}天"
                
                // 通知设置
                textViewNotificationInfo.text = "每${config.notificationFrequency}小时 ${config.notificationTime}"
                
                // 状态指示器
                switchEnabled.isChecked = config.isEnabled
                switchEnabled.setOnCheckedChangeListener { _, isChecked ->
                    onToggleEnabled(config, isChecked)
                }
                
                // 默认配置标识
                if (config.isDefault) {
                    chipDefault.visibility = android.view.View.VISIBLE
                    chipDefault.text = "默认"
                } else {
                    chipDefault.visibility = android.view.View.GONE
                }
                
                // 启用状态样式
                val alpha = if (config.isEnabled) 1.0f else 0.6f
                cardView.alpha = alpha
                
                // 点击事件
                root.setOnClickListener { onItemClick(config) }
                
                buttonEdit.setOnClickListener { onEditClick(config) }
                
                buttonDelete.setOnClickListener { onDeleteClick(config) }
                
                buttonSetDefault.setOnClickListener { onDefaultClick(config) }
                
                // 如果已经是默认配置，隐藏设为默认按钮
                buttonSetDefault.visibility = if (config.isDefault) {
                    android.view.View.GONE
                } else {
                    android.view.View.VISIBLE
                }
            }
        }
    }

    class ConfigDiffCallback : DiffUtil.ItemCallback<ShelfLifeConfig>() {
        override fun areItemsTheSame(oldItem: ShelfLifeConfig, newItem: ShelfLifeConfig): Boolean {
            return oldItem.id == newItem.id
        }

        override fun areContentsTheSame(oldItem: ShelfLifeConfig, newItem: ShelfLifeConfig): Boolean {
            return oldItem == newItem
        }
    }
}
