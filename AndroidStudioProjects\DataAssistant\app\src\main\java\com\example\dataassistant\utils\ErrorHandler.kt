package com.example.dataassistant.utils

import android.content.Context
import android.util.Log
import java.io.IOException
import java.net.SocketTimeoutException
import java.net.UnknownHostException

/**
 * 错误处理工具类
 */
object ErrorHandler {

    private const val TAG = "ErrorHandler"

    /**
     * 处理异常并返回用户友好的错误消息
     */
    fun handleError(throwable: Throwable, context: Context? = null): String {
        Log.e(TAG, "Error occurred", throwable)
        
        return when (throwable) {
            is UnknownHostException -> "网络连接失败，请检查网络设置"
            is SocketTimeoutException -> "网络请求超时，请稍后重试"
            is IOException -> "网络错误，请检查网络连接"
            is IllegalArgumentException -> "参数错误：${throwable.message ?: "未知错误"}"
            is IllegalStateException -> "状态错误：${throwable.message ?: "未知错误"}"
            is SecurityException -> "权限不足，请检查应用权限"
            is OutOfMemoryError -> "内存不足，请关闭其他应用后重试"
            is NullPointerException -> "数据异常，请重新加载"
            else -> {
                val message = throwable.message
                if (!message.isNullOrBlank()) {
                    "操作失败：$message"
                } else {
                    "未知错误，请稍后重试"
                }
            }
        }
    }

    /**
     * 记录错误日志
     */
    fun logError(tag: String, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.e(tag, message, throwable)
        } else {
            Log.e(tag, message)
        }
    }

    /**
     * 记录警告日志
     */
    fun logWarning(tag: String, message: String, throwable: Throwable? = null) {
        if (throwable != null) {
            Log.w(tag, message, throwable)
        } else {
            Log.w(tag, message)
        }
    }

    /**
     * 记录信息日志
     */
    fun logInfo(tag: String, message: String) {
        Log.i(tag, message)
    }

    /**
     * 记录调试日志
     */
    fun logDebug(tag: String, message: String) {
        Log.d(tag, message)
    }

    /**
     * 检查是否为网络相关错误
     */
    fun isNetworkError(throwable: Throwable): Boolean {
        return throwable is UnknownHostException ||
                throwable is SocketTimeoutException ||
                throwable is IOException
    }

    /**
     * 检查是否为权限相关错误
     */
    fun isPermissionError(throwable: Throwable): Boolean {
        return throwable is SecurityException
    }

    /**
     * 检查是否为内存相关错误
     */
    fun isMemoryError(throwable: Throwable): Boolean {
        return throwable is OutOfMemoryError
    }

    /**
     * 获取错误类型
     */
    fun getErrorType(throwable: Throwable): ErrorType {
        return when {
            isNetworkError(throwable) -> ErrorType.NETWORK
            isPermissionError(throwable) -> ErrorType.PERMISSION
            isMemoryError(throwable) -> ErrorType.MEMORY
            throwable is IllegalArgumentException -> ErrorType.VALIDATION
            throwable is IllegalStateException -> ErrorType.STATE
            else -> ErrorType.UNKNOWN
        }
    }

    /**
     * 获取错误建议
     */
    fun getErrorSuggestion(throwable: Throwable): String {
        return when (getErrorType(throwable)) {
            ErrorType.NETWORK -> "请检查网络连接后重试"
            ErrorType.PERMISSION -> "请在设置中授予应用必要权限"
            ErrorType.MEMORY -> "请关闭其他应用释放内存"
            ErrorType.VALIDATION -> "请检查输入的数据是否正确"
            ErrorType.STATE -> "请重新启动应用"
            ErrorType.UNKNOWN -> "请稍后重试，如问题持续请联系客服"
        }
    }

    /**
     * 创建错误报告
     */
    fun createErrorReport(throwable: Throwable, context: String = ""): ErrorReport {
        return ErrorReport(
            type = getErrorType(throwable),
            message = handleError(throwable),
            suggestion = getErrorSuggestion(throwable),
            stackTrace = throwable.stackTraceToString(),
            context = context,
            timestamp = System.currentTimeMillis()
        )
    }

    /**
     * 安全执行代码块
     */
    inline fun <T> safeCall(
        onError: (Throwable) -> T,
        block: () -> T
    ): T {
        return try {
            block()
        } catch (e: Exception) {
            logError("SafeCall", "Error in safe call", e)
            onError(e)
        }
    }

    /**
     * 安全执行代码块（无返回值）
     */
    inline fun safeRun(
        onError: (Throwable) -> Unit = { logError("SafeRun", "Error in safe run", it) },
        block: () -> Unit
    ) {
        try {
            block()
        } catch (e: Exception) {
            onError(e)
        }
    }
}

/**
 * 错误类型枚举
 */
enum class ErrorType {
    NETWORK,        // 网络错误
    PERMISSION,     // 权限错误
    MEMORY,         // 内存错误
    VALIDATION,     // 验证错误
    STATE,          // 状态错误
    UNKNOWN         // 未知错误
}

/**
 * 错误报告数据类
 */
data class ErrorReport(
    val type: ErrorType,
    val message: String,
    val suggestion: String,
    val stackTrace: String,
    val context: String,
    val timestamp: Long
) {
    /**
     * 转换为可读的报告字符串
     */
    fun toReadableString(): String {
        return buildString {
            appendLine("错误类型: ${type.name}")
            appendLine("错误消息: $message")
            appendLine("建议: $suggestion")
            appendLine("上下文: $context")
            appendLine("时间: ${java.text.SimpleDateFormat("yyyy-MM-dd HH:mm:ss", java.util.Locale.getDefault()).format(java.util.Date(timestamp))}")
            appendLine("详细信息:")
            appendLine(stackTrace)
        }
    }
}
