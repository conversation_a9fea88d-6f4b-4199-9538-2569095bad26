<resources xmlns:tools="http://schemas.android.com/tools">
    <!-- Base application theme. -->
    <style name="Base.Theme.DataAssistant" parent="Theme.Material3.DayNight.NoActionBar">
        <!-- Primary colors -->
        <item name="colorPrimary">@color/primary</item>
        <item name="colorPrimaryVariant">@color/primary_dark</item>
        <item name="colorOnPrimary">@color/white</item>

        <!-- Secondary colors -->
        <item name="colorSecondary">@color/accent</item>
        <item name="colorOnSecondary">@color/white</item>

        <!-- Background colors -->
        <item name="android:colorBackground">@color/background</item>
        <item name="colorSurface">@color/surface</item>
        <item name="colorOnBackground">@color/text_primary</item>
        <item name="colorOnSurface">@color/text_primary</item>

        <!-- Status bar -->
        <item name="android:statusBarColor">@color/primary_dark</item>
        <item name="android:windowLightStatusBar">false</item>

        <!-- Navigation bar -->
        <item name="android:navigationBarColor">@color/surface</item>
        <item name="android:windowLightNavigationBar">true</item>
    </style>

    <style name="Theme.DataAssistant" parent="Base.Theme.DataAssistant" />

    <style name="Theme.DataAssistant.AppBarOverlay" parent="ThemeOverlay.Material3.Dark.ActionBar">
        <item name="colorPrimary">@color/primary</item>
        <item name="colorOnPrimary">@color/white</item>
    </style>

    <style name="Theme.DataAssistant.PopupOverlay" parent="ThemeOverlay.Material3.Light" />

</resources>
