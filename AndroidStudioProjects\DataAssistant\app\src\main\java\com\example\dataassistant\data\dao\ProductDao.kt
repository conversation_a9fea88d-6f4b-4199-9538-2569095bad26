package com.example.dataassistant.data.dao

import androidx.lifecycle.LiveData
import androidx.room.*
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus

/**
 * 商品数据访问对象
 */
@Dao
interface ProductDao {

    /**
     * 获取所有商品
     */
    @Query("SELECT * FROM products ORDER BY createTime DESC")
    fun getAllProducts(): LiveData<List<Product>>

    /**
     * 获取启用通知的商品
     */
    @Query("SELECT * FROM products WHERE isNotificationEnabled = 1 ORDER BY expiryDate ASC")
    suspend fun getProductsWithNotificationEnabled(): List<Product>

    /**
     * 根据状态获取商品
     */
    @Query("SELECT * FROM products WHERE status = :status ORDER BY expiryDate ASC")
    fun getProductsByStatus(status: ProductStatus): LiveData<List<Product>>

    /**
     * 获取即将过期的商品（指定天数内）
     */
    @Query("""
        SELECT * FROM products
        WHERE (expiryDate - :currentTime) / (24 * 60 * 60 * 1000) <= :days
        AND (expiryDate - :currentTime) / (24 * 60 * 60 * 1000) >= 0
        ORDER BY expiryDate ASC
    """)
    fun getExpiringProducts(currentTime: Long, days: Int): LiveData<List<Product>>

    /**
     * 获取已过期的商品
     */
    @Query("SELECT * FROM products WHERE expiryDate < :currentTime ORDER BY expiryDate DESC")
    fun getExpiredProducts(currentTime: Long): LiveData<List<Product>>

    /**
     * 根据分类获取商品
     */
    @Query("SELECT * FROM products WHERE category = :category ORDER BY createTime DESC")
    fun getProductsByCategory(category: String): LiveData<List<Product>>

    /**
     * 搜索商品
     */
    @Query("""
        SELECT * FROM products
        WHERE name LIKE '%' || :keyword || '%'
        OR brand LIKE '%' || :keyword || '%'
        OR category LIKE '%' || :keyword || '%'
        ORDER BY createTime DESC
    """)
    fun searchProducts(keyword: String): LiveData<List<Product>>

    /**
     * 根据ID获取商品
     */
    @Query("SELECT * FROM products WHERE id = :id")
    suspend fun getProductById(id: String): Product?

    /**
     * 根据条码获取商品
     */
    @Query("SELECT * FROM products WHERE barcode = :barcode")
    suspend fun getProductByBarcode(barcode: String): List<Product>

    /**
     * 插入商品
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProduct(product: Product)

    /**
     * 批量插入商品
     */
    @Insert(onConflict = OnConflictStrategy.REPLACE)
    suspend fun insertProducts(products: List<Product>)

    /**
     * 更新商品
     */
    @Update
    suspend fun updateProduct(product: Product)

    /**
     * 删除商品
     */
    @Delete
    suspend fun deleteProduct(product: Product)

    /**
     * 根据ID删除商品
     */
    @Query("DELETE FROM products WHERE id = :id")
    suspend fun deleteProductById(id: String)

    /**
     * 批量删除商品
     */
    @Query("DELETE FROM products WHERE id IN (:ids)")
    suspend fun deleteProductsByIds(ids: List<String>)

    /**
     * 更新商品状态
     */
    @Query("UPDATE products SET status = :status, updateTime = :updateTime WHERE id = :id")
    suspend fun updateProductStatus(id: String, status: ProductStatus, updateTime: Long)

    /**
     * 获取商品统计信息
     */
    @Query("SELECT COUNT(*) FROM products")
    suspend fun getTotalCount(): Int

    @Query("SELECT COUNT(*) FROM products WHERE status = :status")
    suspend fun getCountByStatus(status: ProductStatus): Int

    @Query("SELECT COUNT(*) FROM products WHERE category = :category")
    suspend fun getCountByCategory(category: String): Int

    /**
     * 获取所有分类
     */
    @Query("SELECT DISTINCT category FROM products WHERE category IS NOT NULL ORDER BY category")
    suspend fun getAllCategories(): List<String>

    /**
     * 获取所有品牌
     */
    @Query("SELECT DISTINCT brand FROM products WHERE brand IS NOT NULL ORDER BY brand")
    suspend fun getAllBrands(): List<String>

    /**
     * 获取即将过期商品数量
     */
    @Query("""
        SELECT COUNT(*) FROM products
        WHERE (expiryDate - :currentTime) / (24 * 60 * 60 * 1000) <= :days
        AND (expiryDate - :currentTime) / (24 * 60 * 60 * 1000) >= 0
    """)
    suspend fun getExpiringProductsCount(currentTime: Long, days: Int): Int

    /**
     * 获取最近添加的商品
     */
    @Query("SELECT * FROM products ORDER BY createTime DESC LIMIT :limit")
    suspend fun getRecentProducts(limit: Int = 10): List<Product>

    /**
     * 清空所有商品
     */
    @Query("DELETE FROM products")
    suspend fun deleteAllProducts()


}
