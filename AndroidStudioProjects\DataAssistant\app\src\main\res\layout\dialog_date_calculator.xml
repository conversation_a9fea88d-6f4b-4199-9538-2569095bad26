<?xml version="1.0" encoding="utf-8"?>
<ScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    android:layout_width="match_parent"
    android:layout_height="wrap_content"
    android:padding="16dp">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical">

        <!-- 生产日期输入 -->
        <com.google.android.material.textfield.TextInputLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            android:hint="生产日期"
            app:startIconDrawable="@android:drawable/ic_menu_my_calendar">

            <com.google.android.material.textfield.TextInputEditText
                android:id="@+id/edit_text_production_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:clickable="true"
                android:focusable="false"
                android:inputType="none" />

        </com.google.android.material.textfield.TextInputLayout>

        <!-- 保质期输入 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="保质期"
            android:textSize="16sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <com.google.android.material.textfield.TextInputLayout
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:hint="数量">

                <com.google.android.material.textfield.TextInputEditText
                    android:id="@+id/edit_text_shelf_life_number"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:inputType="number" />

            </com.google.android.material.textfield.TextInputLayout>

            <Spinner
                android:id="@+id/spinner_shelf_life_unit"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:layout_gravity="center_vertical"
                android:minWidth="80dp" />

        </LinearLayout>

        <!-- 快捷保质期按钮 -->
        <TextView
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:text="快捷设置"
            android:textSize="14sp"
            android:textStyle="bold"
            android:layout_marginBottom="8dp" />

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="8dp">

            <Button
                android:id="@+id/button_quick_7_days"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="7天"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_quick_15_days"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="15天"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_quick_1_month"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="1月"
                android:textSize="12sp" />

        </LinearLayout>

        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/button_quick_3_months"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="4dp"
                android:text="3月"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_quick_6_months"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:layout_marginEnd="4dp"
                android:text="6月"
                android:textSize="12sp" />

            <Button
                android:id="@+id/button_quick_1_year"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="4dp"
                android:text="1年"
                android:textSize="12sp" />

        </LinearLayout>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginBottom="16dp">

            <Button
                android:id="@+id/button_calculate"
                style="@style/Widget.Material3.Button"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="计算" />

            <Button
                android:id="@+id/button_reset"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="重置" />

        </LinearLayout>

        <!-- 计算结果 -->
        <LinearLayout
            android:id="@+id/layout_results"
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="vertical"
            android:background="@color/background"
            android:padding="16dp"
            android:visibility="gone">

            <TextView
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="计算结果"
                android:textSize="16sp"
                android:textStyle="bold"
                android:layout_marginBottom="12dp" />

            <TextView
                android:id="@+id/text_view_shelf_life_days"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="保质期：30天"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/text_view_expiry_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="到期日期：2024-01-01"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/text_view_warning_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="临期提醒：2023-12-25"
                android:textSize="14sp"
                android:layout_marginBottom="4dp" />

            <TextView
                android:id="@+id/text_view_removal_date"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="下架提醒：2023-12-29"
                android:textSize="14sp"
                android:layout_marginBottom="8dp" />

            <TextView
                android:id="@+id/text_view_remaining_days"
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:text="剩余15天"
                android:textSize="16sp"
                android:textStyle="bold" />

        </LinearLayout>

    </LinearLayout>

</ScrollView>
