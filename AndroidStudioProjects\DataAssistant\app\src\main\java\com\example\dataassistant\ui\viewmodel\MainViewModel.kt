package com.example.dataassistant.ui.viewmodel

import androidx.lifecycle.LiveData
import androidx.lifecycle.MutableLiveData
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.example.dataassistant.data.entity.Product
import com.example.dataassistant.data.entity.ProductStatus
import com.example.dataassistant.data.repository.ProductRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.launch
import javax.inject.Inject

/**
 * 主界面ViewModel
 */
@HiltViewModel
class MainViewModel @Inject constructor(
    private val productRepository: ProductRepository
) : ViewModel() {
    
    private val _uiState = MutableLiveData<MainUiState>()
    val uiState: LiveData<MainUiState> = _uiState
    
    private val _statistics = MutableLiveData<HomeStatistics>()
    val statistics: LiveData<HomeStatistics> = _statistics
    
    private val _recentProducts = MutableLiveData<List<Product>>()
    val recentProducts: LiveData<List<Product>> = _recentProducts
    
    // 实时数据
    val allProducts = productRepository.getAllProducts()
    val warningProducts = productRepository.getProductsByStatus(ProductStatus.WARNING)
    val expiredProducts = productRepository.getProductsByStatus(ProductStatus.EXPIRED)
    
    init {
        _uiState.value = MainUiState()
        loadHomeData()
    }
    
    /**
     * 加载首页数据
     */
    fun loadHomeData() {
        viewModelScope.launch {
            try {
                _uiState.value = _uiState.value?.copy(isLoading = true)
                
                // 加载统计信息
                loadStatistics()
                
                // 加载最近添加的商品
                loadRecentProducts()
                
                _uiState.value = _uiState.value?.copy(isLoading = false)
            } catch (e: Exception) {
                _uiState.value = _uiState.value?.copy(
                    isLoading = false,
                    error = "加载数据失败: ${e.message}"
                )
            }
        }
    }
    
    /**
     * 加载统计信息
     */
    private suspend fun loadStatistics() {
        try {
            val totalCount = productRepository.getTotalCount()
            val normalCount = productRepository.getCountByStatus(ProductStatus.NORMAL)
            val warningCount = productRepository.getCountByStatus(ProductStatus.WARNING)
            val expiredCount = productRepository.getCountByStatus(ProductStatus.EXPIRED)
            val expiringIn7Days = productRepository.getExpiringProductsCount(7)
            val expiringIn3Days = productRepository.getExpiringProductsCount(3)
            
            val statistics = HomeStatistics(
                totalProducts = totalCount,
                normalProducts = normalCount,
                warningProducts = warningCount,
                expiredProducts = expiredCount,
                expiringIn7Days = expiringIn7Days,
                expiringIn3Days = expiringIn3Days
            )
            
            _statistics.value = statistics
        } catch (e: Exception) {
            _uiState.value = _uiState.value?.copy(
                error = "加载统计信息失败: ${e.message}"
            )
        }
    }
    
    /**
     * 加载最近添加的商品
     */
    private suspend fun loadRecentProducts() {
        try {
            val products = productRepository.getRecentProducts(5)
            _recentProducts.value = products
        } catch (e: Exception) {
            _uiState.value = _uiState.value?.copy(
                error = "加载最近商品失败: ${e.message}"
            )
        }
    }
    
    /**
     * 获取即将过期的商品
     */
    fun getExpiringProducts(days: Int = 7) = productRepository.getExpiringProducts(days)
    
    /**
     * 刷新数据
     */
    fun refreshData() {
        loadHomeData()
    }
    
    /**
     * 清除错误信息
     */
    fun clearError() {
        _uiState.value = _uiState.value?.copy(error = null)
    }
    
    /**
     * 清除消息
     */
    fun clearMessage() {
        _uiState.value = _uiState.value?.copy(message = null)
    }
}

/**
 * 主界面UI状态
 */
data class MainUiState(
    val isLoading: Boolean = false,
    val error: String? = null,
    val message: String? = null
)

/**
 * 首页统计信息
 */
data class HomeStatistics(
    val totalProducts: Int = 0,
    val normalProducts: Int = 0,
    val warningProducts: Int = 0,
    val expiredProducts: Int = 0,
    val expiringIn7Days: Int = 0,
    val expiringIn3Days: Int = 0
) {
    /**
     * 获取健康度百分比
     */
    fun getHealthPercentage(): Float {
        return if (totalProducts > 0) {
            (normalProducts.toFloat() / totalProducts.toFloat()) * 100
        } else {
            100f
        }
    }
    
    /**
     * 获取风险等级
     */
    fun getRiskLevel(): RiskLevel {
        val healthPercentage = getHealthPercentage()
        return when {
            healthPercentage >= 80 -> RiskLevel.LOW
            healthPercentage >= 60 -> RiskLevel.MEDIUM
            else -> RiskLevel.HIGH
        }
    }
}

/**
 * 风险等级枚举
 */
enum class RiskLevel {
    LOW,    // 低风险
    MEDIUM, // 中风险
    HIGH    // 高风险
}
