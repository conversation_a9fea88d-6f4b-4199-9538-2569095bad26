package com.example.dataassistant.ui.component

import android.content.Context
import android.util.AttributeSet
import android.view.LayoutInflater
import android.view.View
import android.widget.LinearLayout
import com.example.dataassistant.databinding.ViewLoadingStateBinding

/**
 * 加载状态视图组件
 */
class LoadingStateView @JvmOverloads constructor(
    context: Context,
    attrs: AttributeSet? = null,
    defStyleAttr: Int = 0
) : LinearLayout(context, attrs, defStyleAttr) {

    private val binding: ViewLoadingStateBinding
    
    init {
        binding = ViewLoadingStateBinding.inflate(LayoutInflater.from(context), this, true)
        orientation = VERTICAL
    }

    /**
     * 显示加载状态
     */
    fun showLoading(message: String = "加载中...") {
        binding.apply {
            progressBar.visibility = View.VISIBLE
            textViewMessage.text = message
            textViewMessage.visibility = View.VISIBLE
            buttonRetry.visibility = View.GONE
        }
        visibility = View.VISIBLE
    }

    /**
     * 显示错误状态
     */
    fun showError(message: String = "加载失败", onRetry: (() -> Unit)? = null) {
        binding.apply {
            progressBar.visibility = View.GONE
            textViewMessage.text = message
            textViewMessage.visibility = View.VISIBLE
            
            if (onRetry != null) {
                buttonRetry.visibility = View.VISIBLE
                buttonRetry.setOnClickListener { onRetry() }
            } else {
                buttonRetry.visibility = View.GONE
            }
        }
        visibility = View.VISIBLE
    }

    /**
     * 显示空状态
     */
    fun showEmpty(message: String = "暂无数据", actionText: String? = null, onAction: (() -> Unit)? = null) {
        binding.apply {
            progressBar.visibility = View.GONE
            textViewMessage.text = message
            textViewMessage.visibility = View.VISIBLE
            
            if (actionText != null && onAction != null) {
                buttonRetry.text = actionText
                buttonRetry.visibility = View.VISIBLE
                buttonRetry.setOnClickListener { onAction() }
            } else {
                buttonRetry.visibility = View.GONE
            }
        }
        visibility = View.VISIBLE
    }

    /**
     * 隐藏状态视图
     */
    fun hide() {
        visibility = View.GONE
    }

    /**
     * 检查是否正在显示
     */
    fun isShowing(): Boolean {
        return visibility == View.VISIBLE
    }
}
