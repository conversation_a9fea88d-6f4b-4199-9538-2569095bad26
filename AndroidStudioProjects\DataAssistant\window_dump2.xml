<?xml version='1.0' encoding='UTF-8' standalone='yes' ?><hierarchy rotation="0"><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="0" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/action_bar_root" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="android:id/content" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,2856]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,192]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/toolbar" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,0][1280,192]" drawing-order="1" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/nav_host_fragment_content_main" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/nav_host_fragment_content_main" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/swipe_refresh_layout" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.ScrollView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,192][1280,2664]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,240][1232,795]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,240][1232,795]" drawing-order="1" hint=""><node index="0" text="商品概览" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,288][312,367]" drawing-order="1" hint="" /><node index="1" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,415][1184,637]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/card_total_products" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,415][434,637]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,415][434,637]" drawing-order="1" hint=""><node index="0" text="0" resource-id="com.example.dataassistant:id/text_view_total_count" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[244,451][285,548]" drawing-order="1" hint="" /><node index="1" text="总商品" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[211,548][319,601]" drawing-order="2" hint="" /></node></node><node index="1" text="" resource-id="com.example.dataassistant:id/card_warning_products" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[470,415][809,637]" drawing-order="2" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[470,415][809,637]" drawing-order="1" hint=""><node index="0" text="0" resource-id="com.example.dataassistant:id/text_view_warning_count" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[619,451][660,548]" drawing-order="1" hint="" /><node index="1" text="临期" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[603,548][675,601]" drawing-order="2" hint="" /></node></node><node index="2" text="" resource-id="com.example.dataassistant:id/card_expired_products" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[845,415][1184,637]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[845,415][1184,637]" drawing-order="1" hint=""><node index="0" text="0" resource-id="com.example.dataassistant:id/text_view_expired_count" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[994,451][1035,548]" drawing-order="1" hint="" /><node index="1" text="过期" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[978,548][1050,601]" drawing-order="2" hint="" /></node></node></node><node index="2" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,685][1184,747]" drawing-order="3" hint=""><node index="0" text="库存健康度：" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,685][348,747]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.example.dataassistant:id/progress_bar_health" class="android.widget.ProgressBar" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[372,692][1057,740]" drawing-order="2" hint="" /><node index="2" text="100%" resource-id="com.example.dataassistant:id/text_view_health_percentage" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1081,687][1184,744]" drawing-order="3" hint="" /></node></node></node><node index="1" text="" resource-id="" class="androidx.cardview.widget.CardView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,843][1232,1381]" drawing-order="4" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[48,843][1232,1381]" drawing-order="1" hint=""><node index="0" text="" resource-id="" class="android.widget.LinearLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,891][1184,1035]" drawing-order="1" hint=""><node index="0" text="最近添加" resource-id="" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,923][920,1002]" drawing-order="1" hint="" /><node index="1" text="查看更多" resource-id="com.example.dataassistant:id/button_view_more" class="android.widget.Button" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[920,891][1184,1035]" drawing-order="2" hint="" /></node><node index="1" text="暂无商品数据" resource-id="com.example.dataassistant:id/text_view_empty_state" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[96,1071][1184,1333]" drawing-order="3" hint="" /></node></node></node></node></node></node></node><node index="2" text="" resource-id="com.example.dataassistant:id/bottom_navigation" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2544][1280,2856]" drawing-order="3" hint=""><node index="0" text="" resource-id="" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[0,2544][1280,2784]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_home" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="首页" checkable="false" checked="false" clickable="false" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[0,2544][320,2784]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[64,2580][256,2676]" drawing-order="1" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_active_indicator_view" class="android.view.View" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[64,2580][256,2676]" drawing-order="1" hint="" /><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[124,2592][196,2664]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[123,2694][197,2784]" drawing-order="2" hint=""><node index="0" text="首页" resource-id="com.example.dataassistant:id/navigation_bar_item_large_label_view" class="android.widget.TextView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="true" bounds="[123,2694][197,2747]" drawing-order="2" hint="" /></node></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_add" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="添加商品" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[320,2544][640,2784]" drawing-order="2" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[384,2616][576,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[444,2628][516,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[405,2731][554,2784]" drawing-order="2" hint="" /></node><node index="2" text="" resource-id="com.example.dataassistant:id/navigation_list" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="商品列表" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[640,2544][960,2784]" drawing-order="3" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[704,2616][896,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[764,2628][836,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[725,2731][874,2784]" drawing-order="2" hint="" /></node><node index="3" text="" resource-id="com.example.dataassistant:id/navigation_config" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="配置管理" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[960,2544][1280,2784]" drawing-order="4" hint=""><node index="0" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_container" class="android.widget.FrameLayout" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1024,2616][1216,2712]" drawing-order="1" hint=""><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_icon_view" class="android.widget.ImageView" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1084,2628][1156,2700]" drawing-order="2" hint="" /></node><node index="1" text="" resource-id="com.example.dataassistant:id/navigation_bar_item_labels_group" class="android.view.ViewGroup" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="false" enabled="true" focusable="false" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1045,2731][1194,2784]" drawing-order="2" hint="" /></node></node></node><node NAF="true" index="3" text="" resource-id="com.example.dataassistant:id/fab" class="android.widget.ImageButton" package="com.example.dataassistant" content-desc="" checkable="false" checked="false" clickable="true" enabled="true" focusable="true" focused="false" scrollable="false" long-clickable="false" password="false" selected="false" bounds="[1064,2472][1232,2640]" drawing-order="4" hint="" /></node></node></node></node></node></node></hierarchy>