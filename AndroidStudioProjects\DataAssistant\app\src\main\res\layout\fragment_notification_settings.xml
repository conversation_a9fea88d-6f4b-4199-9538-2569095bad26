<?xml version="1.0" encoding="utf-8"?>
<androidx.core.widget.NestedScrollView xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent"
    android:fillViewport="true">

    <LinearLayout
        android:layout_width="match_parent"
        android:layout_height="wrap_content"
        android:orientation="vertical"
        android:padding="16dp">

        <!-- 通知状态 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="通知状态"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <TextView
                    android:id="@+id/text_view_notification_status"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="通知已启用"
                    android:textSize="16sp"
                    android:layout_marginBottom="8dp"
                    tools:text="通知已启用" />

                <TextView
                    android:id="@+id/text_view_next_notification"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="下次通知: 明天 09:00"
                    android:textSize="14sp"
                    android:textColor="@android:color/darker_gray"
                    tools:text="下次通知: 明天 09:00" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 权限警告 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp"
            app:cardBackgroundColor="@color/status_warning_light">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:id="@+id/text_view_permission_warning"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="需要通知权限才能发送提醒"
                    android:textSize="14sp"
                    android:textColor="@color/status_warning"
                    android:layout_marginBottom="8dp"
                    android:visibility="gone"
                    tools:visibility="visible" />

                <Button
                    android:id="@+id/button_request_permission"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="授予权限"
                    android:visibility="gone"
                    tools:visibility="visible" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 通知设置 -->
        <com.google.android.material.card.MaterialCardView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:layout_marginBottom="16dp"
            app:cardCornerRadius="12dp"
            app:cardElevation="4dp">

            <LinearLayout
                android:layout_width="match_parent"
                android:layout_height="wrap_content"
                android:orientation="vertical"
                android:padding="16dp">

                <TextView
                    android:layout_width="wrap_content"
                    android:layout_height="wrap_content"
                    android:text="通知设置"
                    android:textSize="18sp"
                    android:textStyle="bold"
                    android:layout_marginBottom="12dp" />

                <!-- 启用通知 -->
                <LinearLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:orientation="horizontal"
                    android:gravity="center_vertical"
                    android:layout_marginBottom="16dp">

                    <TextView
                        android:layout_width="0dp"
                        android:layout_height="wrap_content"
                        android:layout_weight="1"
                        android:text="启用通知提醒"
                        android:textSize="16sp" />

                    <Switch
                        android:id="@+id/switch_notification_enabled"
                        android:layout_width="wrap_content"
                        android:layout_height="wrap_content" />

                </LinearLayout>

                <!-- 通知间隔 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="8dp"
                    android:hint="通知间隔（小时）">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_notification_interval"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:inputType="number" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 通知时间 -->
                <com.google.android.material.textfield.TextInputLayout
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:layout_marginBottom="16dp"
                    android:hint="通知时间">

                    <com.google.android.material.textfield.TextInputEditText
                        android:id="@+id/edit_text_notification_time"
                        android:layout_width="match_parent"
                        android:layout_height="wrap_content"
                        android:focusable="false"
                        android:clickable="true"
                        android:inputType="none" />

                </com.google.android.material.textfield.TextInputLayout>

                <!-- 测试通知 -->
                <Button
                    android:id="@+id/button_test_notification"
                    style="@style/Widget.Material3.Button.OutlinedButton"
                    android:layout_width="match_parent"
                    android:layout_height="wrap_content"
                    android:text="测试通知"
                    android:layout_marginBottom="8dp" />

            </LinearLayout>

        </com.google.android.material.card.MaterialCardView>

        <!-- 操作按钮 -->
        <LinearLayout
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:orientation="horizontal"
            android:layout_marginTop="16dp">

            <Button
                android:id="@+id/button_reset_settings"
                style="@style/Widget.Material3.Button.OutlinedButton"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginEnd="8dp"
                android:text="重置" />

            <Button
                android:id="@+id/button_save_settings"
                android:layout_width="0dp"
                android:layout_height="wrap_content"
                android:layout_weight="1"
                android:layout_marginStart="8dp"
                android:text="保存设置" />

        </LinearLayout>

        <!-- 说明文本 -->
        <TextView
            android:layout_width="match_parent"
            android:layout_height="wrap_content"
            android:text="通知将在设定时间检查商品状态，如有临期或过期商品会发送提醒。"
            android:textSize="12sp"
            android:textColor="@android:color/darker_gray"
            android:layout_marginTop="16dp"
            android:gravity="center" />

    </LinearLayout>

</androidx.core.widget.NestedScrollView>
