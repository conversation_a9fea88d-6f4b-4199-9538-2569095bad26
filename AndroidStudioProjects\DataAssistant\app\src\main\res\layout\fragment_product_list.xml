<?xml version="1.0" encoding="utf-8"?>
<androidx.constraintlayout.widget.ConstraintLayout xmlns:android="http://schemas.android.com/apk/res/android"
    xmlns:app="http://schemas.android.com/apk/res-auto"
    xmlns:tools="http://schemas.android.com/tools"
    android:layout_width="match_parent"
    android:layout_height="match_parent">

    <!-- 统计信息 -->
    <TextView
        android:id="@+id/text_view_statistics"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_margin="16dp"
        android:background="@drawable/bg_statistics"
        android:gravity="center"
        android:padding="12dp"
        android:text="总计: 0 | 正常: 0 | 临期: 0 | 过期: 0"
        android:textSize="14sp"
        android:textStyle="bold"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 筛选Chips -->
    <HorizontalScrollView
        android:id="@+id/scroll_view_filters"
        android:layout_width="0dp"
        android:layout_height="wrap_content"
        android:layout_marginTop="8dp"
        android:scrollbars="none"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/text_view_statistics">

        <com.google.android.material.chip.ChipGroup
            android:layout_width="wrap_content"
            android:layout_height="wrap_content"
            android:paddingHorizontal="16dp"
            app:singleSelection="true">

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_all"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:checked="true"
                android:text="全部" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_normal"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="正常" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_warning"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="临期" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_expired"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="过期" />

            <com.google.android.material.chip.Chip
                android:id="@+id/chip_expiring_soon"
                style="@style/Widget.Material3.Chip.Filter"
                android:layout_width="wrap_content"
                android:layout_height="wrap_content"
                android:text="即将过期" />

        </com.google.android.material.chip.ChipGroup>

    </HorizontalScrollView>

    <!-- 商品列表 -->
    <androidx.swiperefreshlayout.widget.SwipeRefreshLayout
        android:id="@+id/swipe_refresh_layout"
        android:layout_width="0dp"
        android:layout_height="0dp"
        android:layout_marginTop="8dp"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toBottomOf="@+id/scroll_view_filters">

        <androidx.recyclerview.widget.RecyclerView
            android:id="@+id/recycler_view_products"
            android:layout_width="match_parent"
            android:layout_height="match_parent"
            android:clipToPadding="false"
            android:paddingBottom="80dp"
            tools:listitem="@layout/item_product" />

    </androidx.swiperefreshlayout.widget.SwipeRefreshLayout>

    <!-- 空状态 -->
    <TextView
        android:id="@+id/text_view_empty"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:text="暂无商品数据"
        android:textColor="@android:color/darker_gray"
        android:textSize="16sp"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

    <!-- 加载指示器 -->
    <ProgressBar
        android:id="@+id/progress_bar"
        android:layout_width="wrap_content"
        android:layout_height="wrap_content"
        android:visibility="gone"
        app:layout_constraintBottom_toBottomOf="parent"
        app:layout_constraintEnd_toEndOf="parent"
        app:layout_constraintStart_toStartOf="parent"
        app:layout_constraintTop_toTopOf="parent" />

</androidx.constraintlayout.widget.ConstraintLayout>
